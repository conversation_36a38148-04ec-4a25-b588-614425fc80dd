import { localStg } from '@/utils/storage';

/** Token storage keys */
export const TOKEN_KEY = 'token';
export const TENANT_ID_KEY = 'tenant_id';

/**
 * Get token from storage
 * @returns {string} Token value
 */
export function getToken() {
  return localStg.get(TOKEN_KEY) || '';
}

/**
 * Get tenant ID from storage
 * @returns {string} Tenant ID value
 */
export function getTenantId() {
  return localStg.get(TENANT_ID_KEY) || '';
}

/**
 * Set token and tenant ID to storage
 * @param {string} token - Auth token
 * @param {string} tenantId - Tenant ID
 */
export function setToken(token, tenantId) {
  localStg.set(TOKEN_KEY, token);
  localStg.set(TENANT_ID_KEY, tenantId);
}

/**
 * Clear auth storage
 */
export function clearAuthStorage() {
  localStg.remove(TOKEN_KEY);
  localStg.remove(TENANT_ID_KEY);
}

/**
 * Check if user is logged in
 * @returns {boolean} True if user has valid token
 */
export function isLoggedIn() {
  return Boolean(getToken());
}

/**
 * Get user info storage key
 * @returns {string} User info storage key
 */
export function getUserInfoKey() {
  return 'user_info';
}

/**
 * Get user permissions storage key
 * @returns {string} User permissions storage key
 */
export function getUserPermissionsKey() {
  return 'user_permissions';
}

/**
 * Get user routes storage key
 * @returns {string} User routes storage key
 */
export function getUserRoutesKey() {
  return 'user_routes';
}
