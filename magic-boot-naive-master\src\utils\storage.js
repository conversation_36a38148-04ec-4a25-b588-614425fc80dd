/**
 * Storage utility functions
 */

/**
 * Create storage instance with prefix support
 * @param {Storage} storage - Storage instance (localStorage or sessionStorage)
 * @param {string} prefix - Storage key prefix
 */
function createStorage(storage, prefix = '') {

  function getKey(key) {
    return prefix ? `${prefix}_${key}` : key;
  }
  return {
    /**
     * Set storage item
     * @param {string} key - Storage key
     * @param {any} value - Storage value
     */
    set(key, value) {
      try {
        const serializedValue = JSON.stringify(value);
        storage.setItem(getKey(key), serializedValue);
      } catch (error) {
        console.error(`Error setting storage item ${key}:`, error);
      }
    },

    /**
     * Get storage item
     * @param {string} key - Storage key
     * @param {any} defaultValue - Default value if key doesn't exist
     * @returns {any} Storage value or default value
     */
    get(key, defaultValue = null) {
      try {
        const item = storage.getItem(getKey(key));
        if (item === null) {
          return defaultValue;
        }
        return JSON.parse(item);
      } catch (error) {
        console.error(`Error getting storage item ${key}:`, error);
        return defaultValue;
      }
    },

    /**
     * Remove storage item
     * @param {string} key - Storage key
     */
    remove(key) {
      try {
        storage.removeItem(getKey(key));
      } catch (error) {
        console.error(`Error removing storage item ${key}:`, error);
      }
    },

    /**
     * Clear all storage items
     */
    clear() {
      try {
        storage.clear();
      } catch (error) {
        console.error('Error clearing storage:', error);
      }
    },

    /**
     * Check if key exists in storage
     * @param {string} key - Storage key
     * @returns {boolean} True if key exists
     */
    has(key) {
      return storage.getItem(getKey(key)) !== null;
    },

    /**
     * Get all keys in storage
     * @returns {string[]} Array of storage keys
     */
    keys() {
      try {
        const keys = Object.keys(storage);
        if (prefix) {
          return keys
            .filter(key => key.startsWith(`${prefix}_`))
            .map(key => key.replace(`${prefix}_`, ''));
        }
        return keys;
      } catch (error) {
        console.error('Error getting storage keys:', error);
        return [];
      }
    }
  };
}

// Storage prefix from environment
const storagePrefix = import.meta.env.VITE_STORAGE_PREFIX || 'magic_boot';

/** Local storage instance */
export const localStg = createStorage(localStorage, storagePrefix);

/** Session storage instance */
export const sessionStg = createStorage(sessionStorage, storagePrefix);

// Legacy compatibility - keep existing usage working
export const storage = {
  get: localStg.get,
  set: localStg.set,
  remove: localStg.remove,
  clear: localStg.clear,
  has: localStg.has,
  keys: localStg.keys
};
