<template>
  <div class="mb-logicflow-property">
    <div class="property-header">
      <h3>属性配置</h3>
    </div>

    <div class="property-content">
      <!-- 节点属性 -->
      <div class="property-section">
        <h4>节点属性</h4>
        
        <div class="form-group">
          <label>功能名称</label>
          <input 
            type="text" 
            v-model="nodeProperties.functionName"
            placeholder="请输入功能名称"
            class="form-input"
            @blur="updateProperties"
            @input="updateProperties"
          />
        </div>
        
        <div class="form-group">
          <label>路由地址</label>
          <input 
            type="text" 
            v-model="nodeProperties.routePath"
            placeholder="请输入路由地址，如：/warehouse-inventory"
            class="form-input"
            @blur="updateProperties"
            @input="updateProperties"
          />
        </div>
        
        <div class="form-group">
          <label>节点描述</label>
          <textarea
            v-model="nodeProperties.description"
            placeholder="请输入节点描述，如：仓库库存功能模块的详细描述"
            rows="3"
            class="form-textarea"
            @blur="updateProperties"
            @input="updateProperties"
          ></textarea>
        </div>
        
        <div class="form-group">
          <label>图标选择</label>
          <div class="icon-selector">
            <div class="current-icon" @click="showIconModal = true">
              <svg v-if="nodeProperties.iconSvg" class="current-icon-svg" viewBox="0 0 24 24">
                <use :xlink:href="nodeProperties.iconSvg" />
              </svg>
              <span v-else class="no-icon">点击选择图标</span>
            </div>
            <button type="button" class="select-icon-btn" @click="showIconModal = true">
              选择图标
            </button>
          </div>
        </div>
        
        <div class="form-group">
          <label>背景颜色</label>
          <div class="color-selector">
            <div class="color-preview" 
                 :style="{ backgroundColor: nodeProperties.backgroundColor || '#ffffff' }"
                 @click="showColorPicker = true">
            </div>
            <input 
              type="color" 
              v-model="nodeProperties.backgroundColor"
              class="color-input"
              @change="updateProperties"
            />
            <div class="color-presets">
              <div 
                v-for="color in colorPresets" 
                :key="color.value"
                class="color-preset-item" 
                :style="{ backgroundColor: color.value }"
                :title="color.name"
                @click="selectPresetColor('backgroundColor', color.value)"
              ></div>
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label>图标颜色</label>
          <div class="color-selector">
            <div class="color-preview" 
                 :style="{ backgroundColor: nodeProperties.iconColor || '#000000' }">
            </div>
            <input 
              type="color" 
              v-model="nodeProperties.iconColor"
              class="color-input"
              @change="updateProperties"
            />
            <div class="color-presets">
              <div 
                v-for="color in iconColorPresets" 
                :key="color.value"
                class="color-preset-item" 
                :style="{ backgroundColor: color.value }"
                :title="color.name"
                @click="selectPresetColor('iconColor', color.value)"
              ></div>
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label>文字颜色</label>
          <div class="color-selector">
            <div class="color-preview" 
                 :style="{ backgroundColor: nodeProperties.textColor || '#000000' }">
            </div>
            <input 
              type="color" 
              v-model="nodeProperties.textColor"
              class="color-input"
              @change="updateProperties"
            />
            <div class="color-presets">
              <div 
                v-for="color in textColorPresets" 
                :key="color.value"
                class="color-preset-item" 
                :style="{ backgroundColor: color.value }"
                :title="color.name"
                @click="selectPresetColor('textColor', color.value)"
              ></div>
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label>节点宽度</label>
          <input 
            type="number" 
            v-model.number="nodeProperties.width"
            placeholder="请输入节点宽度（默认120）"
            class="form-input"
            min="60"
            max="300"
            @blur="updateProperties"
            @input="updateProperties"
          />
        </div>
        
        <div class="form-group">
          <label>节点高度</label>
          <input 
            type="number" 
            v-model.number="nodeProperties.height"
            placeholder="请输入节点高度（默认80）"
            class="form-input"
            min="40"
            max="200"
            @blur="updateProperties"
            @input="updateProperties"
          />
        </div>
      </div>
    </div>

    <!-- 图标选择弹框 -->
    <div v-if="showIconModal" class="icon-modal-overlay" @click="closeModal">
      <div class="icon-modal" @click.stop>
        <div class="modal-header">
          <h3>选择图标</h3>
          <button class="close-btn" @click="closeModal">×</button>
        </div>
        
        <div class="modal-search">
          <input
            type="text"
            v-model="iconSearchKeyword"
            placeholder="搜索图标..."
            class="search-input"
            @input="filterIcons"
          />
        </div>
        
        <div class="modal-content">
          <div class="icon-grid">
            <div
              v-for="icon in filteredIcons"
              :key="icon.name"
              class="icon-item"
              :class="{ active: nodeProperties.iconSvg === icon.svg }"
              @click="selectIcon(icon)"
            >
              <svg class="icon-preview-svg" viewBox="0 0 24 24">
                <use :xlink:href="icon.svg" />
              </svg>
              <span class="icon-name">{{ icon.displayName || icon.name }}</span>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button class="cancel-btn" @click="closeModal">取消</button>
          <button class="clear-btn" @click="clearIcon">清除图标</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, watch, computed, onMounted } from 'vue'

export default {
  name: 'MbLogicflowProperty',
  props: {
    // 完全参考MVP项目的属性传递方式
    elementsStyle: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['updateProperties'],
  setup(props, { emit }) {
    // 节点属性
    const nodeProperties = ref({
      functionName: '',
      routePath: '',
      iconSvg: '',
      description: '',
      width: 120,
      height: 80,
      shapeType: 'rectangle',
      backgroundColor: '#ffffff',  // 默认白色背景
      iconColor: '#000000',       // 默认黑色图标
      textColor: '#000000'        // 默认黑色文字
    })

    // 图标选择相关
    const showIconModal = ref(false)
    const iconSearchKeyword = ref('')
    const svgIcons = ref([])
    const filteredIcons = ref([])
    
    // 颜色选择相关
    const showColorPicker = ref(false)
    const colorPresets = [
      { name: '白色', value: '#ffffff' },      // 白色放在第一位作为默认
      { name: '浅紫色', value: '#f3f0ff' },
      { name: '浅蓝色', value: '#e6f7ff' },
      { name: '浅绿色', value: '#f6ffed' },
      { name: '浅黄色', value: '#fffbe6' },
      { name: '浅红色', value: '#fff1f0' },
      { name: '浅灰色', value: '#fafafa' },
      { name: '浅橙色', value: '#fff7e6' }
    ]
    
    // 图标颜色预设
    const iconColorPresets = [
      { name: '黑色', value: '#000000' },      // 黑色放在第一位作为默认
      { name: '白色', value: '#ffffff' },
      { name: '深灰', value: '#666666' },
      { name: '紫色', value: '#8b5cf6' },
      { name: '蓝色', value: '#1890ff' },
      { name: '绿色', value: '#52c41a' },
      { name: '橙色', value: '#fa8c16' },
      { name: '红色', value: '#ff4d4f' }
    ]
    
    // 文字颜色预设
    const textColorPresets = [
      { name: '黑色', value: '#000000' },      // 黑色放在第一位作为默认
      { name: '深灰', value: '#333333' },
      { name: '中灰', value: '#666666' },
      { name: '浅灰', value: '#999999' },
      { name: '蓝色', value: '#1890ff' },
      { name: '绿色', value: '#52c41a' },
      { name: '橙色', value: '#fa8c16' },
      { name: '红色', value: '#ff4d4f' }
    ]

    /**
     * 加载SVG图标
     */
    const loadSvgIcons = async () => {
      try {
        // 使用项目的SVG图标系统（src/icons/目录）
        // 直接引用svg-icons.js中的图标列表
        const svgIconNames = await import('@/scripts/svg-icons.js')
        const iconNames = svgIconNames.default || []

        const icons = iconNames.map(iconName => {
          return {
            name: iconName,
            svg: `#mb-icon-${iconName}`,  // 使用symbol引用格式
            symbolId: `#mb-icon-${iconName}`,
            displayName: iconName.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())  // 格式化显示名称
          }
        })

        svgIcons.value = icons
        filteredIcons.value = icons
        console.log('📁 属性面板成功加载SVG图标:', icons.length, '个（使用sprite系统）')
      } catch (error) {
        console.error('❌ 属性面板加载SVG图标失败:', error)
        svgIcons.value = []
        filteredIcons.value = []
      }
    }

    /**
     * 过滤图标
     */
    const filterIcons = () => {
      if (!iconSearchKeyword.value) {
        filteredIcons.value = svgIcons.value
      } else {
        filteredIcons.value = svgIcons.value.filter(icon => 
          icon.name.toLowerCase().includes(iconSearchKeyword.value.toLowerCase())
        )
      }
    }

    /**
     * 选择图标
     */
    const selectIcon = (icon) => {
      nodeProperties.value.iconSvg = icon.svg
      console.log('🎯 选择图标:', icon.name, icon.svg)
      updateProperties()
      closeModal()
    }

    /**
     * 清除图标
     */
    const clearIcon = () => {
      nodeProperties.value.iconSvg = ''
      console.log('🗑️ 清除图标')
      updateProperties()
      closeModal()
    }

    /**
     * 关闭弹框
     */
    const closeModal = () => {
      showIconModal.value = false
      iconSearchKeyword.value = ''
    }
    
    /**
     * 选择预设颜色
     */
    const selectPresetColor = (colorType, color) => {
      nodeProperties.value[colorType] = color
      console.log(`🎨 选择${colorType}:`, color)
      updateProperties()
    }

    /**
     * 监听属性变化，更新本地数据（完全参考MVP项目）
     */
    watch(() => props.elementsStyle, (newStyle) => {
      console.log('🔍 属性面板接收到新属性:', newStyle)
      
      // 更新节点属性
      nodeProperties.value = {
        functionName: newStyle.functionName || '',
        routePath: newStyle.routePath || '',
        iconSvg: newStyle.iconSvg || '',
        description: newStyle.description || '',
        width: newStyle.width || 120,
        height: newStyle.height || 80,
        shapeType: newStyle.shapeType || 'rectangle',
        backgroundColor: newStyle.backgroundColor || '#ffffff',  // 默认白色背景
        iconColor: newStyle.iconColor || '#000000',       // 默认黑色图标
        textColor: newStyle.textColor || '#000000'        // 默认黑色文字
      }
      
      console.log('📋 属性面板已更新:', nodeProperties.value)
    }, { immediate: true, deep: true })

    /**
     * 更新属性 - 完全参考MVP项目的setStyle事件
     */
    const updateProperties = () => {
      const allProperties = {
        ...nodeProperties.value
      }
      
      console.log('📝 发送属性更新事件:', allProperties)
      
      // 发送setStyle事件，与MVP项目保持一致
      emit('updateProperties', {
        type: 'setStyle',
        properties: allProperties
      })
    }

    onMounted(() => {
      loadSvgIcons()
    })

    return {
      nodeProperties,
      showIconModal,
      iconSearchKeyword,
      filteredIcons,
      selectIcon,
      clearIcon,
      closeModal,
      filterIcons,
      updateProperties,
      showColorPicker,
      colorPresets,
      iconColorPresets,
      textColorPresets,
      selectPresetColor
    }
  }
}
</script>

<style scoped>
.mb-logicflow-property {
  width: 100%;
  height: 100%;
  padding: 16px;
  background: #ffffff;
  border-left: 1px solid #e8e8e8;
  overflow-y: auto;
  /* 确保内容可见 */
  position: relative;
  z-index: 1;
  /* 添加盒模型设置，确保padding不会导致溢出 */
  box-sizing: border-box;
}

.property-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  /* 文字换行处理 */
  word-wrap: break-word;
  word-break: break-word;
}

.property-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  /* 文字换行处理 */
  word-wrap: break-word;
  word-break: break-word;
}

.property-section {
  margin-bottom: 24px;
}

.property-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #595959;
  /* 文字换行处理 */
  word-wrap: break-word;
  word-break: break-word;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #8c8c8c;
  /* 文字换行处理 */
  word-wrap: break-word;
  word-break: break-word;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 13px;
  color: #262626;
  background: #ffffff;
  transition: all 0.2s;
  /* 确保输入框不会溢出容器 */
  box-sizing: border-box;
  max-width: 100%;
  overflow-x: hidden;
  word-wrap: break-word;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-textarea {
  resize: vertical;
  min-height: 60px;
}

/* 图标选择器 */
.icon-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-icon {
  width: 40px;
  height: 40px;
  border: 2px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.current-icon:hover {
  border-color: #1890ff;
}

.current-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.no-icon {
  font-size: 10px;
  color: #bfbfbf;
  text-align: center;
  line-height: 1.2;
}

.select-icon-btn {
  flex: 1;
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  color: #595959;
  cursor: pointer;
  transition: all 0.2s;
}

.select-icon-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

/* 图标选择弹框 */
.icon-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.icon-modal {
  background: #ffffff;
  border-radius: 8px;
  width: 480px;
  max-width: 90vw;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #8c8c8c;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #262626;
}

.modal-search {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.search-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.modal-content {
  flex: 1;
  padding: 16px 24px;
  overflow-y: auto;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 12px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.icon-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.icon-item.active {
  border-color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.icon-item img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  margin-bottom: 6px;
}

/* SVG图标样式 */
.current-icon-svg {
  width: 32px;
  height: 32px;
  fill: currentColor;
}

.icon-preview-svg {
  width: 32px;
  height: 32px;
  fill: currentColor;
  margin-bottom: 6px;
}

.icon-name {
  font-size: 10px;
  color: #595959;
  text-align: center;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn, .clear-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: #ffffff;
  color: #595959;
}

.cancel-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.clear-btn {
  background: #ff4d4f;
  color: #ffffff;
  border-color: #ff4d4f;
}

.clear-btn:hover {
  background: #ff7875;
  border-color: #ff7875;
}

/* 颜色选择器样式 */
.color-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.color-preview {
  width: 32px;
  height: 32px;
  border: 2px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.color-preview:hover {
  border-color: #1890ff;
  transform: scale(1.05);
}

.color-input {
  width: 40px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  background: none;
}

.color-presets {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.color-preset-item {
  width: 24px;
  height: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.color-preset-item:hover {
  border-color: #1890ff;
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .mb-logicflow-property {
    padding: 12px;
  }
  
  .form-input, .form-textarea {
    font-size: 12px;
    padding: 6px 10px;
  }
}

@media (max-width: 768px) {
  .mb-logicflow-property {
    padding: 8px;
  }
  
  .property-header h3 {
    font-size: 14px;
  }
  
  .property-section h4 {
    font-size: 12px;
  }
  
  .form-group label {
    font-size: 11px;
  }
  
  .form-input, .form-textarea {
    font-size: 11px;
    padding: 6px 8px;
  }
  
  .icon-modal {
    width: 95vw;
  }
  
  .icon-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 8px;
  }
}
</style> 