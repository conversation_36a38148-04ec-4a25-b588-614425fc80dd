<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/userStore';
import MbIcon from '@/components/magic/basic/mb-icon.vue';

defineOptions({
  name: 'UserAvatar'
});

const router = useRouter();
const userStore = useUserStore();

// 用户信息
const userInfo = computed(() => userStore.getInfo);

// 用户头像
const userAvatar = computed(() => {
  if (userInfo.value.headPortrait) {
    return window.$global?.baseApi + userInfo.value.headPortrait;
  }
  return null;
});

// 用户名首字母
const userInitial = computed(() => {
  return userInfo.value.name?.substring(0, 1) || 'U';
});

// 下拉菜单选项
const dropdownOptions = computed(() => [
  {
    label: '个人中心',
    key: 'userCenter',
    icon: () => h(MbIcon, { icon: 'PersonOutline', size: '16px' })
  },
  {
    type: 'divider',
    key: 'divider'
  },
  {
    label: '退出登录',
    key: 'logout',
    icon: () => h(MbIcon, { icon: 'LogOut', size: '16px' })
  }
]);

// 处理下拉菜单选择
function handleDropdownSelect(key) {
  switch (key) {
    case 'userCenter':
      // 跳转到个人中心
      router.push('/user/profile');
      break;
    case 'logout':
      handleLogout();
      break;
  }
}

// 处理退出登录
function handleLogout() {
  window.$dialog?.info({
    title: '提示',
    content: '确定要退出登录吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      userStore.logout();
    }
  });
}
</script>

<script>
import { h } from 'vue';
</script>

<template>
  <n-dropdown
    :options="dropdownOptions"
    @select="handleDropdownSelect"
  >
    <div class="user-avatar-container flex items-center cursor-pointer">
      <!-- 用户头像 -->
      <n-avatar
        v-if="userAvatar"
        round
        :size="32"
        :src="userAvatar"
        class="user-avatar"
      />
      <n-avatar
        v-else
        round
        :size="32"
        class="user-avatar"
      >
        {{ userInitial }}
      </n-avatar>
      
      <!-- 用户名 -->
      <span v-if="userInfo.name" class="user-name ml-2 text-sm">
        {{ userInfo.name }}
      </span>
      
      <!-- 下拉箭头 -->
      <n-icon size="14" class="ml-1 dropdown-arrow">
        <svg viewBox="0 0 24 24">
          <path fill="currentColor" d="M7 10l5 5 5-5z"/>
        </svg>
      </n-icon>
    </div>
  </n-dropdown>
</template>

<style scoped>
.user-avatar-container {
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.user-avatar-container:hover {
  background-color: var(--n-item-color-hover);
}

.user-avatar {
  flex-shrink: 0;
}

.user-name {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--n-text-color);
}

.dropdown-arrow {
  color: var(--n-text-color-disabled);
  transition: transform 0.2s ease;
}

.user-avatar-container:hover .dropdown-arrow {
  color: var(--n-text-color);
}
</style>
