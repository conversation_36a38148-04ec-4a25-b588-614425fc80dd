import { computed, effectScope, onScopeDispose, ref, watch } from 'vue';
import { defineStore } from 'pinia';
import { useEventListener, usePreferredColorScheme } from '@vueuse/core';
import { localStg } from '@/utils/storage';
import { 
  initThemeSettings, 
  createThemeToken, 
  getNaiveTheme, 
  addThemeVarsToGlobal,
  toggleCssDarkMode,
  toggleAuxiliaryColorModes
} from '@/theme/shared';

/** Theme store */
export const useThemeStore = defineStore('theme', () => {
  const scope = effectScope();
  const osTheme = usePreferredColorScheme();

  /** Theme settings */
  const settings = ref(initThemeSettings());

  /** Dark mode */
  const darkMode = computed(() => {
    if (settings.value.themeScheme === 'auto') {
      return osTheme.value === 'dark';
    }
    return settings.value.themeScheme === 'dark';
  });

  /** Theme scheme */
  const themeScheme = computed(() => (darkMode.value ? 'dark' : 'light'));

  /** Grayscale mode */
  const grayscaleMode = computed(() => settings.value.grayscale);

  /** Colour weakness mode */
  const colourWeaknessMode = computed(() => settings.value.colourWeakness);

  /** Theme colors */
  const themeColors = computed(() => {
    const { themeColor, otherColor, isInfoFollowPrimary } = settings.value;
    const colors = {
      primary: themeColor,
      ...otherColor,
      info: isInfoFollowPrimary ? themeColor : otherColor.info
    };
    return colors;
  });

  /** Naive theme */
  const naiveTheme = computed(() => getNaiveTheme(themeColors.value, settings.value.recommendColor));

  /** Layout */
  const layout = computed(() => settings.value.layout);

  /** Header */
  const header = computed(() => settings.value.header);

  /** Tab */
  const tab = computed(() => settings.value.tab);

  /** Sider */
  const sider = computed(() => settings.value.sider);

  /** Footer */
  const footer = computed(() => settings.value.footer);

  /** Fixed header and tab */
  const fixedHeaderAndTab = computed(() => settings.value.fixedHeaderAndTab);

  /** Page */
  const page = computed(() => settings.value.page);

  /** Watermark */
  const watermark = computed(() => settings.value.watermark);

  /**
   * Settings json
   * It is for copy settings
   */
  const settingsJson = computed(() => JSON.stringify(settings.value));

  /** Reset store */
  function resetStore() {
    settings.value = initThemeSettings();
  }

  /**
   * Set theme scheme
   * @param {string} themeScheme - Theme scheme
   */
  function setThemeScheme(themeScheme) {
    settings.value.themeScheme = themeScheme;
  }

  /** Toggle theme scheme */
  function toggleThemeScheme() {
    const themeSchemes = ['light', 'dark', 'auto'];
    const currentIndex = themeSchemes.indexOf(settings.value.themeScheme);
    const nextIndex = (currentIndex + 1) % themeSchemes.length;
    setThemeScheme(themeSchemes[nextIndex]);
  }

  /**
   * Set grayscale
   * @param {boolean} grayscale - Grayscale mode
   */
  function setGrayscale(grayscale) {
    settings.value.grayscale = grayscale;
  }

  /**
   * Set colour weakness
   * @param {boolean} colourWeakness - Colour weakness mode
   */
  function setColourWeakness(colourWeakness) {
    settings.value.colourWeakness = colourWeakness;
  }

  /**
   * Update theme colors
   * @param {string} key - Theme color key
   * @param {string} color - Theme color
   */
  function updateThemeColors(key, color) {
    let colorValue = color;

    if (settings.value.recommendColor) {
      // Use the color as is for now, can be enhanced later
      colorValue = color;
    }

    if (key === 'primary') {
      settings.value.themeColor = colorValue;
    } else {
      settings.value.otherColor[key] = colorValue;
    }
  }

  /**
   * Set theme color
   * @param {string} themeColor - Theme color
   */
  function setThemeColor(themeColor) {
    settings.value.themeColor = themeColor;
  }

  /**
   * Set other color
   * @param {string} key - Color key
   * @param {string} color - Color value
   */
  function setOtherColor(key, color) {
    settings.value.otherColor[key] = color;
  }

  /**
   * Set info follow primary
   * @param {boolean} isInfoFollowPrimary - Whether info color follows primary
   */
  function setInfoFollowPrimary(isInfoFollowPrimary) {
    settings.value.isInfoFollowPrimary = isInfoFollowPrimary;
  }

  /**
   * Set theme layout
   * @param {string} mode - Theme layout mode
   */
  function setThemeLayout(mode) {
    settings.value.layout.mode = mode;
  }

  /**
   * Set layout scroll mode
   * @param {string} scrollMode - Scroll mode
   */
  function setLayoutScrollMode(scrollMode) {
    settings.value.layout.scrollMode = scrollMode;
  }

  /**
   * Set layout reverse horizontal mix
   * @param {boolean} reverse - Reverse horizontal mix
   */
  function setLayoutReverseHorizontalMix(reverse) {
    settings.value.layout.reverseHorizontalMix = reverse;
  }

  /**
   * Set header height
   * @param {number} height - Header height
   */
  function setHeaderHeight(height) {
    settings.value.header.height = height;
  }

  /**
   * Set tab visible
   * @param {boolean} visible - Tab visible
   */
  function setTabVisible(visible) {
    settings.value.tab.visible = visible;
  }

  /**
   * Set tab height
   * @param {number} height - Tab height
   */
  function setTabHeight(height) {
    settings.value.tab.height = height;
  }

  /**
   * Set sider width
   * @param {number} width - Sider width
   */
  function setSiderWidth(width) {
    settings.value.sider.width = width;
  }

  /**
   * Set sider collapsed width
   * @param {number} width - Sider collapsed width
   */
  function setSiderCollapsedWidth(width) {
    settings.value.sider.collapsedWidth = width;
  }

  /**
   * Set sider inverted
   * @param {boolean} inverted - Sider inverted
   */
  function setSiderInverted(inverted) {
    settings.value.sider.inverted = inverted;
  }

  /** Setup theme vars to global */
  function setupThemeVarsToGlobal() {
    const { themeTokens, darkThemeTokens } = createThemeToken(
      themeColors.value,
      settings.value.tokens,
      settings.value.recommendColor
    );
    addThemeVarsToGlobal(themeTokens, darkThemeTokens);
  }

  /** Cache theme settings */
  function cacheThemeSettings() {
    const isProd = import.meta.env.PROD;

    if (!isProd) return;

    localStg.set('themeSettings', settings.value);
  }

  /** Reset theme settings */
  function resetThemeSettings() {
    settings.value = initThemeSettings();
  }

  // cache theme settings when page is closed or refreshed
  useEventListener(window, 'beforeunload', () => {
    cacheThemeSettings();
  });

  // watch store
  scope.run(() => {
    // watch dark mode
    watch(
      darkMode,
      val => {
        toggleCssDarkMode(val);
        localStg.set('darkMode', val);
      },
      { immediate: true }
    );

    watch(
      [grayscaleMode, colourWeaknessMode],
      val => {
        toggleAuxiliaryColorModes(val[0], val[1]);
      },
      { immediate: true }
    );

    // themeColors change, update css vars and storage theme color
    watch(
      themeColors,
      val => {
        setupThemeVarsToGlobal();
        localStg.set('themeColor', val.primary);
      },
      { immediate: true }
    );
  });

  /** On scope dispose */
  onScopeDispose(() => {
    scope.stop();
  });

  return {
    settings,
    darkMode,
    themeScheme,
    grayscaleMode,
    colourWeaknessMode,
    themeColors,
    naiveTheme,
    layout,
    header,
    tab,
    sider,
    footer,
    fixedHeaderAndTab,
    page,
    watermark,
    settingsJson,
    resetStore,
    setThemeScheme,
    toggleThemeScheme,
    updateThemeColors,
    setThemeColor,
    setOtherColor,
    setInfoFollowPrimary,
    setThemeLayout,
    setLayoutScrollMode,
    setLayoutReverseHorizontalMix,
    setHeaderHeight,
    setTabVisible,
    setTabHeight,
    setSiderWidth,
    setSiderCollapsedWidth,
    setSiderInverted,
    setGrayscale,
    setColourWeakness,
    setupThemeVarsToGlobal,
    cacheThemeSettings,
    resetThemeSettings
  };
});
