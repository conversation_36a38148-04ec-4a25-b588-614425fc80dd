import type { ModuleOptions } from '../utils/index.js';
export type Options = ModuleOptions & {
    caseSensitive?: boolean;
    caseSensitiveStrict?: boolean;
};
export type MessageId = 'unresolved' | 'casingMismatch';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageId, [(Options | undefined)?], import("../utils/create-rule.ts").ImportXPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
