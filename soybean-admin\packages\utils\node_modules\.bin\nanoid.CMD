@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\workspace\FrontendProjects\vue\ui-refactor\soybean-admin\node_modules\.pnpm\nanoid@5.1.5\node_modules\nanoid\bin\node_modules;D:\workspace\FrontendProjects\vue\ui-refactor\soybean-admin\node_modules\.pnpm\nanoid@5.1.5\node_modules\nanoid\node_modules;D:\workspace\FrontendProjects\vue\ui-refactor\soybean-admin\node_modules\.pnpm\nanoid@5.1.5\node_modules;D:\workspace\FrontendProjects\vue\ui-refactor\soybean-admin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\workspace\FrontendProjects\vue\ui-refactor\soybean-admin\node_modules\.pnpm\nanoid@5.1.5\node_modules\nanoid\bin\node_modules;D:\workspace\FrontendProjects\vue\ui-refactor\soybean-admin\node_modules\.pnpm\nanoid@5.1.5\node_modules\nanoid\node_modules;D:\workspace\FrontendProjects\vue\ui-refactor\soybean-admin\node_modules\.pnpm\nanoid@5.1.5\node_modules;D:\workspace\FrontendProjects\vue\ui-refactor\soybean-admin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\nanoid\bin\nanoid.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\nanoid\bin\nanoid.js" %*
)
