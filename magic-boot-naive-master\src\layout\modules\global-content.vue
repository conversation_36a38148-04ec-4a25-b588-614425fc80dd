<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { useAppStore } from '@/store/modules/appStore';
import { useTabsStore } from '@/store/modules/tabsStore';

defineOptions({
  name: 'GlobalContent'
});

const route = useRoute();
const appStore = useAppStore();
const tabsStore = useTabsStore();

// 获取缓存的iframe组件
const keepaliveIframes = computed(() => {
  return tabsStore.getTabs?.filter(tab => {
    return tab.meta?.iframe && tab.meta?.keepAlive;
  }) || [];
});

// 获取缓存的动态组件
const keepaliveDynamicComponents = computed(() => {
  return tabsStore.getTabs?.filter(tab => {
    return tab.meta?.componentName && tab.meta?.keepAlive;
  }) || [];
});
</script>

<template>
  <div class="global-content h-full">
    <!-- Iframe Components -->
    <template v-for="com in keepaliveIframes" :key="com.path">
      <iframe
        v-show="com.path === route.path"
        :src="com.meta.path"
        class="w-full h-full border-0"
        frameborder="0"
      />
    </template>

    <!-- Dynamic Components -->
    <template v-for="com in keepaliveDynamicComponents" :key="com.path">
      <component
        :is="com.meta.componentName"
        v-show="com.path === route.path"
        class="w-full h-full"
      />
    </template>

    <!-- Router View -->
    <div class="router-content h-full">
      <router-view v-if="appStore.reloadFlag" />
    </div>
  </div>
</template>

<style scoped>
.global-content {
  height: 100%;
  position: relative;
}

.router-content {
  height: 100%;
  overflow: auto;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.border-0 {
  border-width: 0;
}
</style>
