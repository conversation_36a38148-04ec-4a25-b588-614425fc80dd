import type { LegacyImportResolver, LegacyResolver, LegacyResolverObject, ResolvedResult } from 'eslint-import-context';
export declare function resolveWithLegacyResolver(resolver: LegacyResolver, config: unknown, modulePath: string, sourceFile: string): ResolvedResult;
export declare function normalizeConfigResolvers(resolvers: LegacyImportResolver, sourceFile: string): Required<LegacyResolverObject>[];
export declare const LEGACY_NODE_RESOLVERS: Set<string>;
