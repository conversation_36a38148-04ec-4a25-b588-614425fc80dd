import type { ModuleOptions } from '../utils/index.js';
export type Options = ModuleOptions & {
    preventRenamingBindings?: boolean;
};
export type MessageId = 'renameDefault';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"renameDefault", [(Options | undefined)?], import("../utils/create-rule.ts").ImportXPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
