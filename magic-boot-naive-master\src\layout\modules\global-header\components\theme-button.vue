<script setup>
import { useThemeStore } from '@/store/modules/themeStore';

defineOptions({
  name: 'ThemeButton'
});

const themeStore = useThemeStore();

// 切换主题
function toggleTheme() {
  themeStore.toggleThemeScheme();
}
</script>

<template>
  <n-tooltip placement="bottom">
    <template #trigger>
      <n-button
        quaternary
        circle
        @click="toggleTheme"
        class="theme-button"
      >
        <template #icon>
          <n-icon size="18">
            <!-- 深色模式图标 -->
            <svg v-if="themeStore.darkMode" viewBox="0 0 24 24">
              <path 
                fill="currentColor" 
                d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z" 
              />
            </svg>
            <!-- 浅色模式图标 -->
            <svg v-else viewBox="0 0 24 24">
              <path 
                fill="currentColor" 
                d="M12 2.25a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-1.5 0V3a.75.75 0 0 1 .75-.75ZM7.5 12a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM18.894 6.166a.75.75 0 0 0-1.06-1.06l-1.591 1.59a.75.75 0 1 0 1.06 1.061l1.591-1.59ZM21.75 12a.75.75 0 0 1-.75.75h-2.25a.75.75 0 0 1 0-1.5H21a.75.75 0 0 1 .75.75ZM17.834 18.894a.75.75 0 0 0 1.06-1.06l-1.59-1.591a.75.75 0 1 0-1.061 1.06l1.59 1.591ZM12 18a.75.75 0 0 1 .75.75V21a.75.75 0 0 1-1.5 0v-2.25A.75.75 0 0 1 12 18ZM7.758 17.303a.75.75 0 0 0-1.061-1.06l-1.591 1.59a.75.75 0 0 0 1.06 1.061l1.591-1.59ZM6 12a.75.75 0 0 1-.75.75H3a.75.75 0 0 1 0-1.5h2.25A.75.75 0 0 1 6 12ZM6.697 7.757a.75.75 0 0 0 1.06-1.06l-1.59-1.591a.75.75 0 0 0-1.061 1.06l1.59 1.591Z" 
              />
            </svg>
          </n-icon>
        </template>
      </n-button>
    </template>
    {{ themeStore.darkMode ? '切换到浅色模式' : '切换到深色模式' }}
  </n-tooltip>
</template>

<style scoped>
.theme-button {
  transition: all 0.2s ease;
}

.theme-button:hover {
  background-color: var(--n-item-color-hover);
}
</style>
