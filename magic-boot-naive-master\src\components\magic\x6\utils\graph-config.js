// 图形配置工具
import { Graph, Shape } from '@antv/x6'

// 默认画布配置
export const getDefaultGraphOptions = () => {
  return {
    container: null, // 容器元素，需要在组件中指定
    width: 800,
    height: 600,
    grid: {
      visible: true,
      type: 'doubleMesh',
      args: [
        {
          color: '#eee',
          thickness: 1,
        },
        {
          color: '#ddd',
          thickness: 1,
          factor: 4,
        },
      ],
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl',
      minScale: 0.5,
      maxScale: 3,
    },
    connecting: {
      router: 'manhattan',
      connector: {
        name: 'rounded',
        args: {
          radius: 8,
        },
      },
      anchor: 'center',
      connectionPoint: 'boundary',
      allowBlank: false,
      snap: {
        radius: 20,
      },
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: '#5F95FF',
              strokeWidth: 1,
              targetMarker: {
                name: 'classic',
                size: 8,
              },
            },
          },
          router: {
            name: 'manhattan',
          },
        })
      },
      validateConnection({ sourceView, targetView, sourceMagnet, targetMagnet }) {
        if (sourceView === targetView) {
          return false
        }
        if (!sourceMagnet) {
          return false
        }
        if (!targetMagnet) {
          return false
        }
        return true
      },
    },
    highlighting: {
      magnetAvailable: {
        name: 'stroke',
        args: {
          padding: 4,
          attrs: {
            strokeWidth: 2,
            stroke: '#52c41a',
          },
        },
      },
    },
    snapline: true,
    history: true,
    clipboard: true,
    keyboard: true,
    selecting: {
      enabled: true,
      rubberband: true,
      showNodeSelectionBox: true,
    },
    scroller: {
      enabled: true,
      pannable: true,
    },
  }
}

// 初始化图形
export const initGraph = (container, options = {}) => {
  const defaultOptions = getDefaultGraphOptions()
  
  console.log('初始化画布', {
    container, 
    containerSize: container ? { 
      width: container.clientWidth, 
      height: container.clientHeight,
      offsetWidth: container.offsetWidth,
      offsetHeight: container.offsetHeight
    } : null
  })
  
  // 确保容器有合理的尺寸
  const width = options.width || container.clientWidth || 800
  const height = options.height || container.clientHeight || 500
  
  const graph = new Graph({
    ...defaultOptions,
    ...options,
    container,
    width,
    height,
  })
  
  return graph
}

// 注册节点和边
export const registerNodesAndEdges = () => {
  // 这里可以注册自定义节点和边
  // 例如：Graph.registerNode(...)
}

// 导出图形数据为JSON
export const exportGraphData = (graph) => {
  if (!graph) return null
  
  return graph.toJSON()
}

// 从JSON导入图形数据
export const importGraphData = (graph, data) => {
  if (!graph || !data) return false
  
  graph.fromJSON(data)
  return true
} 