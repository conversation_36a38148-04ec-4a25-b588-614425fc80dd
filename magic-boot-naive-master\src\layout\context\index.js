import { computed, ref, watch, h } from 'vue';
import { useRoute } from 'vue-router';
import { useUserStore } from '@/store/modules/userStore';
import MbIcon from '@/components/magic/basic/mb-icon.vue';

/**
 * Setup mix menu context
 */
export function setupMixMenuContext() {
  const route = useRoute();
  const userStore = useUserStore();
  
  const activeFirstLevelMenuKey = ref('');
  
  function setActiveFirstLevelMenuKey(key) {
    activeFirstLevelMenuKey.value = key;
  }
  
  function getActiveFirstLevelMenuKey() {
    const currentPath = route.path;
    const pathSegments = currentPath.split('/').filter(Boolean);
    
    if (pathSegments.length > 0) {
      setActiveFirstLevelMenuKey('/' + pathSegments[0]);
    }
  }
  
  // 获取所有菜单
  const allMenus = computed(() => {
    const menus = userStore.getPermissionRouters || [];
    return processMenusForMix(menus);
  });
  
  // 获取第一级菜单（不包含子菜单）
  const firstLevelMenus = computed(() => 
    allMenus.value.map(menu => {
      const { children, ...rest } = menu;
      return rest;
    })
  );
  
  // 获取当前激活的第一级菜单的子菜单
  const childLevelMenus = computed(() => {
    if (!activeFirstLevelMenuKey.value) return [];
    
    const activeMenu = allMenus.value.find(menu => menu.key === activeFirstLevelMenuKey.value);
    return activeMenu?.children || [];
  });
  
  // 检查当前激活的第一级菜单是否有子菜单
  const isActiveFirstLevelMenuHasChildren = computed(() => {
    return childLevelMenus.value.length > 0;
  });
  
  // 处理菜单数据以适配混合布局
  function processMenusForMix(menus) {
    return menus.map(menu => ({
      label: menu.title,
      key: menu.path,
      routeKey: menu.path,
      icon: menu.icon,
      children: menu.children ? processMenusForMix(menu.children).map(child => ({
        ...child,
        icon: child.icon ? () => h(MbIcon, { icon: child.icon, size: '18px' }) : undefined
      })) : undefined
    }));
  }
  
  // 监听路由变化，自动设置激活的第一级菜单
  watch(
    () => route.path,
    () => {
      getActiveFirstLevelMenuKey();
    },
    { immediate: true }
  );
  
  return {
    allMenus,
    firstLevelMenus,
    childLevelMenus,
    activeFirstLevelMenuKey,
    isActiveFirstLevelMenuHasChildren,
    setActiveFirstLevelMenuKey,
    getActiveFirstLevelMenuKey
  };
}

/**
 * Use menu context for regular menu
 */
export function useMenu() {
  const route = useRoute();
  
  const selectedKey = computed(() => route.path);
  
  return {
    selectedKey
  };
}
