<template>
    <div class="main-container">
        <div class="card-title" style="margin: 0.45rem 0 0.25rem 0;">打印物料明细</div>
        <mb-search :where="tableOptions.where" @search="reloadTable" class="search-row" />

        <mb-table class="table-container" ref="table" v-bind="tableOptions" @selectedRow="selectDetailRow" />
        <div class="form-container">
            <div class="card-title">
                <n-space>
                    <span>打印详情</span>
                    <n-button type="info" @click="handlePrintPreview" :loading="previewLoading">
                        <template #icon>
                            <mb-icon icon="EyeOutline" />
                        </template>
                        打印预览
                    </n-button>
                    <n-button type="info" @click="handlePrint" :loading="printLoading">
                        <template #icon>
                            <mb-icon icon="PrintOutline" />
                        </template>
                        打印
                    </n-button>
                </n-space>
            </div>
            <n-row gutter="24">
                <n-col :span="6">
                    <n-descriptions class="desInfo" label-placement="left" bordered :column="1" size="medium">
                        <n-descriptions-item label="采购订单">
                            {{ curDetailRow.billnum }}
                        </n-descriptions-item>
                        <n-descriptions-item label="物料编码">
                            {{ curDetailRow.itemNo }}
                        </n-descriptions-item>
                        <n-descriptions-item label="物料名称">
                            {{ curDetailRow.itemName }}
                        </n-descriptions-item>
                        <n-descriptions-item label="物料描述" :span="2">
                            {{ curDetailRow.itemDesc }}
                        </n-descriptions-item>
                        <n-descriptions-item label="备注">
                            {{ curDetailRow.memo }}
                        </n-descriptions-item>
                    </n-descriptions>
                </n-col>
                <n-col :span="5">
                    <n-descriptions class="desInfo" label-placement="left" bordered :column="1" size="medium">
                        <n-descriptions-item label="供应商编号">
                            {{ curDetailRow.suppnumber }}
                        </n-descriptions-item>
                        <n-descriptions-item label="特记事项">
                            {{ `1` }}
                        </n-descriptions-item>
                        <n-descriptions-item label="环保标识">
                            {{ `环保标识` }}
                        </n-descriptions-item>
                        <n-descriptions-item label="物料描述">
                            {{ curDetailRow.itemDesc }}
                        </n-descriptions-item>
                        <n-descriptions-item label="标签类型">
                            {{ `标签类型` }}
                        </n-descriptions-item>
                    </n-descriptions>
                </n-col>
                <n-col :span="5">
                    <n-descriptions class="desInfo" label-placement="left" bordered :column="1" size="medium">
                        <n-descriptions-item label="打印标签数">
                            {{ 1 }}
                        </n-descriptions-item>
                        <n-descriptions-item label="档位">
                            {{ `档位` }}
                        </n-descriptions-item>
                        <n-descriptions-item label="未收数量">
                            {{ curDetailRow.nomesQty }}
                        </n-descriptions-item>
                        <n-descriptions-item label="订单数量">
                            {{ curDetailRow.quantity }}
                        </n-descriptions-item>
                        <n-descriptions-item label="已收数量">
                            {{ curDetailRow.mesQty }}
                        </n-descriptions-item>
                    </n-descriptions>
                </n-col>
                <n-col :span="6">
                    <n-descriptions class="desInfo" label-placement="left" bordered :column="1" size="medium">
                        <n-descriptions-item label="保质期(天)">
                            <n-input v-model:value="cacheFormData.bzq" />
                        </n-descriptions-item>
                        <n-descriptions-item label="生产日期">
                            <n-date-picker v-model:formatted-value="cacheFormData.createTime" type="date"
                                value-format="yyyy-MM-dd" />
                        </n-descriptions-item>
                        <n-descriptions-item label="条码每包数量">
                            <n-input v-model:value="cacheFormData.printPackCount" />
                        </n-descriptions-item>
                        <n-descriptions-item label="条码数量">
                            <n-input v-model:value="cacheFormData.perPackBarCount" />
                        </n-descriptions-item>
                    </n-descriptions>
                </n-col>
            </n-row>
        </div>

        <!-- 打印预览对话框 -->
        <n-modal v-model:show="showPrintPreview" preset="dialog" title="打印预览" style="width: 90%; max-width: 1200px;">
            <template #header>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <mb-icon icon="EyeOutline" />
                    <span>打印预览</span>
                </div>
            </template>

            <div class="print-preview-container">
                <div v-if="previewLoading" class="preview-loading">
                    <n-spin size="large">
                        <template #description>
                            正在生成预览...
                        </template>
                    </n-spin>
                </div>
                <div v-else-if="previewHtml" class="preview-content" v-html="previewHtml"></div>
                <div v-else class="preview-empty">
                    <n-empty description="暂无预览内容" />
                </div>
            </div>

            <template #action>
                <n-space>
                    <n-button @click="showPrintPreview = false">
                        关闭
                    </n-button>
                    <n-button type="primary" @click="handlePrint" :loading="printLoading">
                        <template #icon>
                            <mb-icon icon="PrintOutline" />
                        </template>
                        确认打印
                    </n-button>
                </n-space>
            </template>
        </n-modal>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useMessage } from 'naive-ui' // 引入 useMessage 以便使用消息提示

// Naive UI 的消息提示
const $message = useMessage() // 示例中使用了 $message，这里需要初始化

// 打印预览相关状态
const showPrintPreview = ref(false)
const previewHtml = ref('')
const previewLoading = ref(false)
const printLoading = ref(false)

const orderRowId = ref(null)

const table = ref({}) // 主表 mb-table ref
const cacheFormData = ref({
    createTime: null,
})

const tableOptions = ref({
    url: '/srm/bar/barPrintQuery',
    page: true,
    where: {
        billnum: {
            label: '采购单号',
            component: 'input',
            props: {
                clearable: true,
                placeholder: '请输入采购单号'
            }
        },
        pbillDate: {
            label: '日期',
            component: 'date',
            props: {
                type: 'daterange',
                clearable: true,
                width: '400px'
            },
            width: '400px'
        },
        suppName: {
            label: '供应商',
            component: 'input',
            props: {
                clearable: true,
                placeholder: '请输入供应商'
            }
        },
    },
    cols: [
        {
            "field": "billnum",
            "label": "采购单号",
            "width": 100
        },
        {
            "field": "billdate",
            "label": "采购日期",
            "width": 100,
            templet(row) {
                return row.billdate.split(' ')[0]
            }
        },
        {
            "field": "needdate",
            "label": "需求日期",
            "width": 100,
            templet(row) {
                return row.needdate.split(' ')[0]
            }
        },
        {
            "field": "pbillType",
            "label": "单据类型",
            "width": 120
        },
        {
            "field": "suppName",
            "label": "供应商",
            "width": 200
        },
        {
            "field": "itemNo",
            "label": "物料编码",
            "width": 120
        },
        {
            "field": "itemName",
            "label": "物料名称",
            "width": 120
        },
        {
            "field": "itemModel",
            "label": "规格型号",
            "width": 220
        },
        {
            "field": "unit",
            "label": "单位",
            "width": 80
        },
        {
            "field": "quantity",
            "label": "订单数量",
            "width": 120
        },
        {
            "field": "barprintqty",
            "label": "已打印数量",
            "width": 120
        },
        {
            "field": "mesStatus",
            "label": "状态",
            "width": 120
        },
        {
            "field": "actived",
            "label": "是否使用",
            "width": 120
        },
        {
            "field": "memo",
            "label": "备注",
            "width": 120
        },
        {
            "field": "maker",
            "label": "创建人",
            "width": 120
        },
        {
            "field": "makedate",
            "label": "创建日期",
            "width": 120
        },
        {
            "field": "modifyby",
            "label": "修改人",
            "width": 120
        },
        {
            "field": "modifydate",
            "label": "修改日期",
            "width": 120
        },
        {
            "field": "factroy",
            "label": "工厂",
            "width": 120
        },
        {
            "field": "company",
            "label": "公司",
            "width": 120
        }
    ],

    // 双击事件，加载数据到打印面板
    onDblclick: ({ row }) => {
        orderRowId.value = row.sguid; // 更新当前选中的订单ID
        if (!orderRowId.value) {
            $message.warning("请先选择一行数据")
            return
        }
        if (row) {
            curDetailRow.value = row
            console.log('dbl click row:', curDetailRow.value)
        } else {
            curDetailRow.value = {} // 如果没有明细行，清空明细表单
        }
    },
})

const detailPrintTemplate = {
    "panels": [
        {
            "index": 0,
            "name": 1,
            "paperType": "自定义",
            "height": 45,
            "width": 64,
            "paperHeader": 0,
            "paperFooter": 127.55905511811025,
            "printElements": [
                {
                    "options": {
                        "left": 36,
                        "top": 16.5,
                        "height": 30,
                        "width": 100,
                        "textType": "barcode",
                        "title": "barcode",
                        "field": "barcode",
                        "coordinateSync": false,
                        "widthHeightSync": false,
                        "qrCodeLevel": 0
                    },
                    "printElementType": {
                        "title": "barcode",
                        "type": "text"
                    }
                },
                {
                    "options": {
                        "left": 36,
                        "top": 60,
                        "height": 9.75,
                        "width": 120,
                        "title": "产品编码",
                        "field": "number",
                        "coordinateSync": false,
                        "widthHeightSync": false,
                        "qrCodeLevel": 0
                    },
                    "printElementType": {
                        "title": "文本",
                        "type": "text"
                    }
                },
                {
                    "options": {
                        "left": 36,
                        "top": 81,
                        "height": 9.75,
                        "width": 120,
                        "title": "产品名称",
                        "field": "name",
                        "coordinateSync": false,
                        "widthHeightSync": false,
                        "qrCodeLevel": 0
                    },
                    "printElementType": {
                        "title": "文本",
                        "type": "text"
                    }
                }
            ],
            "paperNumberLeft": 151,
            "paperNumberTop": 105,
            "paperNumberContinue": true,
            "overPrintOptions": {
                "content": "",
                "opacity": 0.7,
                "type": 1
            },
            "watermarkOptions": {
                "content": "",
                "fillStyle": "rgba(184, 184, 184, 0.3)",
                "fontSize": "14px",
                "rotate": 25,
                "width": 200,
                "height": 200,
                "timestamp": false,
                "format": "YYYY-MM-DD HH:mm"
            },
            "panelLayoutOptions": {
                "layoutType": "column",
                "layoutRowGap": 0,
                "layoutColumnGap": 0
            }
        }
    ]
}

const handlePrint = async () => {
    try {
        printLoading.value = true
        const res = await $common.post('/srm/bar/barprint', curDetailRow.value);
        const printDatas = res.data.map(item => {
            return {
                number: item.expFieldname1,
                name: item.expFieldname3,
                barcode: item.expFieldname2
            }
        })
        console.log('print', printDatas);
        const hiprintTemplate = new hiprint.PrintTemplate({
            template: detailPrintTemplate, // 模板json对象
        });
        hiprintTemplate.print(printDatas);
        $message.success('打印任务已发送')
    } catch (error) {
        console.error('打印失败:', error)
        $message.error('打印失败')
    } finally {
        printLoading.value = false
    }
}

// 打印预览功能
const handlePrintPreview = async () => {
    try {
        previewLoading.value = true
        const res = await $common.post('/srm/bar/barprint', curDetailRow.value);
        const printDatas = res.data.map(item => {
            return {
                number: item.expFieldname1,
                name: item.expFieldname3,
                barcode: item.expFieldname2
            }
        })

        const hiprintTemplate = new hiprint.PrintTemplate({
            template: detailPrintTemplate, // 模板json对象
        });

        // 获取预览 html
        const jqueryObj = hiprintTemplate.getHtml(printDatas);
        const html = jqueryObj.html();

        // 设置预览内容并显示对话框
        previewHtml.value = html
        showPrintPreview.value = true
        console.log('预览HTML生成成功', html);
    } catch (error) {
        console.error('生成预览失败:', error)
        $message.error('生成预览失败')
    } finally {
        previewLoading.value = false
    }
}
const reloadTable = () => {
    table.value.reload()
}

const curDetailRow = ref({}) // 用于明细行的表单

const selectDetailRow = (row) => {
    console.log('选中了明细行', row)
    orderRowId.value = row.sguid
    curDetailRow.value = row
}

onMounted(() => {
    curDetailRow.value = {
        sguid: "103380",
        smainguid: "101980",
        billnum: "CG001922",
        billdate: "2025-07-03 00:00:00",
        rowno: "1",
        itemNo: "*********",
        quantity: 23870,
        unit: "Pcs",
        depNo: null,
        mesQty: null,
        mesStatus: null,
        actived: 1,
        memo: null,
        maker: null,
        makedate: null,
        modifyby: null,
        modifydate: null,
        factroy: null,
        company: null,
        barprintqty: null,
        curprintcnt: 0,
        printpackcount: 0,
        perpackbarcount: 0,
        scdate: "",
        batchNo: "",
        bzq: "",
        needdate: "2025-07-03 00:00:00",
        jhdate: null,
        suppnote: null,
        returnnum: null,
        fdelete: 0,
        shquan: null,
        itemName: "复合纸",
        itemDesc: null,
        itemModel: "0.13*49.3*135mm  AMA",
        pbillType: "标准采购订单",
        depName: null,
        nomesQty: 23870,
        suppnumber: "EN00012",
        suppName: "惠州市富威绝缘材料科技有限公司"
    };
})
</script>

<style scoped lang="less">
.main-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding-left: 1rem;

    .card-title {
        height: 20px;
        line-height: 20px;
        padding-left: 16px;
        position: relative;
        font-size: 1rem;
        font-weight: normal;
        margin-bottom: 0.5rem;
    }

    .card-title::before {
        position: absolute;
        content: '';
        height: 100%;
        width: 4px;
        background: #0869bd;
        left: 0px;
    }
}


.header-row {
    height: fit-content;
    padding: 16px 0;
}

.search-row {
    padding-bottom: 12px;
}

.table-container {
    flex-basis: 40%;
}

.form-container {
    flex-basis: 60%;
    width: 100%;
    box-shadow: 0 0 10px 0 #e9e9e9;
    padding: 0.75rem 0;

    .desInfo {
        box-shadow: 0 0 10px 0 #e9e9e9;
    }

    :deep(.n-input__border) {
        display: none;
    }

    :deep(.n-descriptions-table-header) {
        width: 110px;
    }

    .footer {
        position: relative;
        width: 100%;

        .form-button {
            position: absolute;
            left: 80%;
            display: flex;
            justify-content: center;
            gap: 1rem;
            padding: 1rem;
        }
    }

}

/* 打印预览对话框样式 */
.print-preview-container {
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #e0e0e6;
    border-radius: 6px;
    background-color: #fafafa;
}

.preview-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.preview-content {
    padding: 20px;
    background-color: white;
    margin: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

/* 确保预览内容中的样式正确显示 */
.preview-content :deep(table) {
    width: 100%;
    border-collapse: collapse;
}

.preview-content :deep(td),
.preview-content :deep(th) {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.preview-content :deep(th) {
    background-color: #f5f5f5;
    font-weight: bold;
}

/* 抽屉内部的表单样式，与原先 Tab 内部的样式保持一致 */

.order-form {
    .form-item-wrapper {
        width: 320px;
        display: inline-flex;
        margin: 4px 8px;

        .n-form-item-blank {
            flex: 1;
            color: red;
        }

        .item-input {
            flex: 1;
        }
    }
}
</style>
<style>
.n-tab-pane {
    display: inline-flex;
    flex-direction: column;
}

.n-tabs-pane-wrapper {
    flex: 1;
    width: 100%;
}

.n-form-item-blank {
    flex: 1;
}

/* scoped 样式：保证所有 label 宽度一致、带底色 */
.compact-form .n-form-item-label {
    background-color: #f0f0f0;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 100px;
    /* 与 label-width 对应 */
    justify-content: flex-end;
    margin-right: 8px;
    text-align: center;
}
</style>
