<script setup>
import { computed } from 'vue';
import { useThemeStore } from '@/store/modules/themeStore';
import { useAppStore } from '@/store/modules/appStore';
import GlobalMenu from './global-menu/index.vue';

defineOptions({
  name: 'GlobalSider'
});

const themeStore = useThemeStore();
const appStore = useAppStore();

const showLogo = computed(() => {
  return (themeStore.layout.mode === 'vertical' || themeStore.layout.mode === 'vertical-mix') && !appStore.isMobile;
});

const darkMenu = computed(() => {
  return themeStore.sider.inverted;
});

const menuWrapperClass = computed(() => {
  return 'flex-1 overflow-hidden';
});
</script>

<template>
  <div class="size-full flex flex-col shadow-sider" :class="{ 'bg-inverted': darkMenu }">
    <!-- Logo -->
    <div
      v-if="showLogo"
      class="logo-container flex items-center justify-center"
      :style="{ height: themeStore.header.height + 'px' }"
    >
      <div class="logo flex items-center">
        <span class="text-xl font-bold" :class="{ 'text-white': darkMenu, 'text-primary': !darkMenu }">
          {{ $global?.title || 'MagicBoot' }}
        </span>
      </div>
    </div>
    
    <!-- Menu -->
    <div id="global-sider-menu" :class="menuWrapperClass">
      <GlobalMenu />
    </div>
  </div>
</template>

<style scoped>
.shadow-sider {
  box-shadow: var(--sider-box-shadow, 2px 0 8px 0 rgb(29, 35, 41, 0.05));
}

.bg-inverted {
  background-color: var(--inverted-color, rgb(0, 20, 40));
}

.text-primary {
  color: var(--primary-color, #646cff);
}

.logo-container {
  border-bottom: 1px solid var(--border-color, rgba(239, 239, 245, 1));
}

.size-full {
  width: 100%;
  height: 100%;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.overflow-hidden {
  overflow: hidden;
}

.text-xl {
  font-size: 1.25rem;
}

.font-bold {
  font-weight: 700;
}

.text-white {
  color: white;
}
</style>
