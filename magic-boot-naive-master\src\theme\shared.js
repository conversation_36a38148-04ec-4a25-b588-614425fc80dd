import { defu } from 'defu';
import { localStg } from '@/utils/storage';
import { themeSettings, overrideThemeSettings } from './settings';
import { themeVars } from './vars';

/** Dark mode class name */
export const DARK_CLASS = 'dark';

/**
 * Init theme settings
 */
export function initThemeSettings() {
  const isProd = import.meta.env.PROD;

  // if it is development mode, the theme settings will not be cached
  if (!isProd) return themeSettings;

  // if it is production mode, the theme settings will be cached in localStorage
  const localSettings = localStg.get('themeSettings');

  let settings = defu(localSettings, themeSettings);

  const buildTime = import.meta.env.VITE_BUILD_TIME || Date.now().toString();
  const isOverride = localStg.get('overrideThemeFlag') === buildTime;

  if (!isOverride) {
    settings = defu(overrideThemeSettings, settings);
    localStg.set('overrideThemeFlag', buildTime);
  }

  return settings;
}

/**
 * Get color palette
 * @param {string} color - Base color
 * @param {boolean} [recommended=false] - Use recommended color
 */
function getColorPalette(color, recommended = false) {
  const colorMap = new Map();
  const colorNumbers = [50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950];

  // Generate color palette based on the base color
  const baseColor = color;

  // Set the base color as 500
  colorMap.set(500, baseColor);

  // Generate lighter colors (50-400)
  colorMap.set(50, lightenColor(baseColor, 0.9));
  colorMap.set(100, lightenColor(baseColor, 0.8));
  colorMap.set(200, lightenColor(baseColor, 0.6));
  colorMap.set(300, lightenColor(baseColor, 0.4));
  colorMap.set(400, lightenColor(baseColor, 0.2));

  // Generate darker colors (600-950)
  colorMap.set(600, darkenColor(baseColor, 0.1));
  colorMap.set(700, darkenColor(baseColor, 0.2));
  colorMap.set(800, darkenColor(baseColor, 0.3));
  colorMap.set(900, darkenColor(baseColor, 0.4));
  colorMap.set(950, darkenColor(baseColor, 0.5));

  return colorMap;
}

/**
 * Lighten color
 * @param {string} color - Color in hex format
 * @param {number} amount - Amount to lighten (0-1)
 */
function lightenColor(color, amount) {
  const num = parseInt(color.replace('#', ''), 16);
  const amt = Math.round(2.55 * amount * 100);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
}

/**
 * Darken color
 * @param {string} color - Color in hex format
 * @param {number} amount - Amount to darken (0-1)
 */
function darkenColor(color, amount) {
  const num = parseInt(color.replace('#', ''), 16);
  const amt = Math.round(2.55 * amount * 100);
  const R = (num >> 16) - amt;
  const G = (num >> 8 & 0x00FF) - amt;
  const B = (num & 0x0000FF) - amt;
  return '#' + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
    (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
    (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
}

/**
 * Create theme palette colors
 * @param {Object} colors - Theme colors
 * @param {boolean} [recommended=false] - Use recommended color
 */
function createThemePaletteColors(colors, recommended = false) {
  const colorKeys = Object.keys(colors);
  const colorPaletteVar = {};

  colorKeys.forEach(key => {
    const colorMap = getColorPalette(colors[key], recommended);

    colorPaletteVar[key] = colorMap.get(500);

    colorMap.forEach((hex, number) => {
      colorPaletteVar[`${key}-${number}`] = hex;
    });
  });

  return colorPaletteVar;
}

/**
 * Create theme token css vars value by theme settings
 * @param {Object} colors - Theme colors
 * @param {Object} tokens - Theme setting tokens
 * @param {boolean} [recommended=false] - Use recommended color
 */
export function createThemeToken(colors, tokens, recommended = false) {
  const paletteColors = createThemePaletteColors(colors, recommended);

  const { light, dark } = tokens || themeSettings.tokens;

  const themeTokens = {
    colors: {
      ...paletteColors,
      nprogress: paletteColors.primary,
      ...light.colors
    },
    boxShadow: {
      ...light.boxShadow
    }
  };

  const darkThemeTokens = {
    colors: {
      ...paletteColors,
      nprogress: paletteColors.primary,
      ...dark.colors
    },
    boxShadow: {
      ...light.boxShadow
    }
  };

  return { themeTokens, darkThemeTokens };
}

/**
 * Get RGB values from color string
 * @param {string} color - Color string
 */
function getRgb(color) {
  // Check if color is valid
  if (!color || typeof color !== 'string') {
    console.warn('getRgb: Invalid color value:', color);
    return { r: 0, g: 0, b: 0 };
  }

  // Simple RGB extraction for hex colors
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return { r, g, b };
  }

  // For rgb() format
  const match = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (match) {
    return {
      r: parseInt(match[1]),
      g: parseInt(match[2]),
      b: parseInt(match[3])
    };
  }

  // Default fallback
  console.warn('getRgb: Unrecognized color format:', color);
  return { r: 0, g: 0, b: 0 };
}

/**
 * Get css var by tokens
 * @param {Object} tokens - Theme tokens
 */
function getCssVarByTokens(tokens) {
  const styles = [];

  function removeVarPrefix(value) {
    return value.replace('var(', '').replace(')', '');
  }

  function removeRgbPrefix(value) {
    return value.replace('rgb(', '').replace(')', '');
  }

  for (const [key, tokenValues] of Object.entries(themeVars)) {
    for (const [tokenKey, tokenValue] of Object.entries(tokenValues)) {
      let cssVarsKey = removeVarPrefix(tokenValue);
      let cssValue = tokens[key] && tokens[key][tokenKey];

      // Skip if cssValue is undefined or null
      if (cssValue === undefined || cssValue === null) {
        console.warn(`getCssVarByTokens: Missing value for ${key}.${tokenKey}`);
        continue;
      }

      if (key === 'colors') {
        cssVarsKey = removeRgbPrefix(cssVarsKey);
        const { r, g, b } = getRgb(cssValue);
        cssValue = `${r} ${g} ${b}`;
      }

      styles.push(`${cssVarsKey}: ${cssValue}`);
    }
  }

  const styleStr = styles.join(';');
  return styleStr;
}

/**
 * Add theme vars to global
 * @param {Object} tokens - Light theme tokens
 * @param {Object} darkTokens - Dark theme tokens
 */
export function addThemeVarsToGlobal(tokens, darkTokens) {
  const cssVarStr = getCssVarByTokens(tokens);
  const darkCssVarStr = getCssVarByTokens(darkTokens);

  const css = `
    :root {
      ${cssVarStr}
    }
  `;

  const darkCss = `
    html.${DARK_CLASS} {
      ${darkCssVarStr}
    }
  `;

  const styleId = 'theme-vars';
  const style = document.querySelector(`#${styleId}`) || document.createElement('style');

  style.id = styleId;
  style.textContent = css + darkCss;

  document.head.appendChild(style);
}

/**
 * Toggle css dark mode
 * @param {boolean} [darkMode=false] - Is dark mode
 */
export function toggleCssDarkMode(darkMode = false) {
  const htmlElement = document.documentElement;
  
  if (darkMode) {
    htmlElement.classList.add(DARK_CLASS);
  } else {
    htmlElement.classList.remove(DARK_CLASS);
  }
}

/**
 * Toggle auxiliary color modes
 * @param {boolean} [grayscaleMode=false] - Grayscale mode
 * @param {boolean} [colourWeakness=false] - Colour weakness mode
 */
export function toggleAuxiliaryColorModes(grayscaleMode = false, colourWeakness = false) {
  const htmlElement = document.documentElement;
  htmlElement.style.filter = [
    grayscaleMode ? 'grayscale(100%)' : '', 
    colourWeakness ? 'invert(80%)' : ''
  ].filter(Boolean).join(' ');
}

/**
 * Get naive theme colors
 * @param {Object} colors - Theme colors
 * @param {boolean} [recommended=false] - Use recommended color
 */
function getNaiveThemeColors(colors, recommended = false) {
  const { primary, info, success, warning, error } = colors;
  
  return {
    primaryColor: primary,
    primaryColorHover: lightenColor(primary, 0.1),
    primaryColorPressed: darkenColor(primary, 0.1),
    primaryColorSuppl: lightenColor(primary, 0.2),
    infoColor: info,
    infoColorHover: lightenColor(info, 0.1),
    infoColorPressed: darkenColor(info, 0.1),
    successColor: success,
    successColorHover: lightenColor(success, 0.1),
    successColorPressed: darkenColor(success, 0.1),
    warningColor: warning,
    warningColorHover: lightenColor(warning, 0.1),
    warningColorPressed: darkenColor(warning, 0.1),
    errorColor: error,
    errorColorHover: lightenColor(error, 0.1),
    errorColorPressed: darkenColor(error, 0.1)
  };
}

/**
 * Get naive theme
 * @param {Object} colors - Theme colors
 * @param {boolean} [recommended=false] - Use recommended color
 */
export function getNaiveTheme(colors, recommended = false) {
  const { primary: colorLoading } = colors;

  const theme = {
    common: {
      ...getNaiveThemeColors(colors, recommended),
      borderRadius: '6px'
    },
    LoadingBar: {
      colorLoading
    },
    Tag: {
      borderRadius: '6px'
    }
  };

  return theme;
}
