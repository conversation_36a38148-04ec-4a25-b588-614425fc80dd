import { localStg } from '@/utils/storage';

/** Mobile breakpoint */
export const MOBILE_WIDTH = 768;

/**
 * Check if current viewport is mobile
 * @returns {boolean} True if mobile
 */
export function checkIsMobile() {
  const { innerWidth } = window;
  return innerWidth <= MOBILE_WIDTH;
}

/**
 * Get backup theme setting key
 * @returns {string} Storage key for backup theme settings
 */
export function getBackupThemeSettingKey() {
  return 'backup_theme_setting_before_mobile';
}

/**
 * Save theme setting backup before mobile
 * @param {Object} backup - Backup data
 */
export function saveThemeBackup(backup) {
  localStg.set(getBackupThemeSettingKey(), backup);
}

/**
 * Get theme setting backup
 * @returns {Object|null} Backup data or null
 */
export function getThemeBackup() {
  return localStg.get(getBackupThemeSettingKey());
}

/**
 * Remove theme setting backup
 */
export function removeThemeBackup() {
  localStg.remove(getBackupThemeSettingKey());
}

/**
 * Get app state storage key
 * @returns {string} Storage key for app state
 */
export function getAppStateKey() {
  return 'app_state';
}

/**
 * Save app state to storage
 * @param {Object} state - App state
 */
export function saveAppState(state) {
  localStg.set(getAppStateKey(), {
    siderCollapse: state.siderCollapse,
    fullContent: state.fullContent,
    contentXScrollable: state.contentXScrollable
  });
}

/**
 * Get app state from storage
 * @returns {Object|null} App state or null
 */
export function getAppState() {
  return localStg.get(getAppStateKey());
}

/**
 * Default app state
 * @returns {Object} Default state object
 */
export function getDefaultAppState() {
  return {
    isMobile: false,
    siderCollapse: false,
    fullContent: false,
    contentXScrollable: false,
    reloadFlag: true
  };
}
