@font-face {
    font-family: PoetsenOne;
    src: url(./fonts/PoetsenOne.woff2) format('woff2');
    font-weight: 100;
    font-style: normal;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.2s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.mb-list {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.mb-search {
    @apply pb-2;
}

// 表格搜索item 间距
.mb-search .n-form .n-form-item {
    margin-bottom: 0.5rem;
}

.mb-search .n-form-item .n-form-item-feedback-wrapper {
    min-height: 0px;
}

.mb-toolbar:not(:empty) {
    @apply pb-2;
}

.mb-table {
    @apply pb-2 box-border;
    flex: 1;
}