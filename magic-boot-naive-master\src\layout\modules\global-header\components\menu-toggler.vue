<script setup>
import { computed } from 'vue';

defineOptions({
  name: '<PERSON><PERSON><PERSON>oggler'
});

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click']);

// 图标状态
const iconRotation = computed(() => {
  return props.collapsed ? 'rotate-0' : 'rotate-180';
});

// 处理点击
function handleClick() {
  emit('click');
}
</script>

<template>
  <n-tooltip placement="bottom">
    <template #trigger>
      <n-button
        quaternary
        circle
        @click="handleClick"
        class="menu-toggler"
      >
        <template #icon>
          <n-icon size="18" :class="iconRotation">
            <svg viewBox="0 0 24 24">
              <path 
                fill="currentColor" 
                d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z" 
              />
            </svg>
          </n-icon>
        </template>
      </n-button>
    </template>
    {{ collapsed ? '展开菜单' : '收起菜单' }}
  </n-tooltip>
</template>

<style scoped>
.menu-toggler {
  transition: all 0.2s ease;
}

.menu-toggler:hover {
  background-color: var(--n-item-color-hover);
}

.rotate-0 {
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}
</style>
