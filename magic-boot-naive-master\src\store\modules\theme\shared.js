import { localStg } from '@/utils/storage';

/** Theme storage keys */
export const THEME_COLOR_KEY = 'theme_color';
export const DARK_MODE_KEY = 'dark_mode';
export const THEME_SETTINGS_KEY = 'theme_settings';

/**
 * Get theme color from storage
 * @returns {string} Theme color
 */
export function getThemeColor() {
  return localStg.get(THEME_COLOR_KEY) || '#646cff';
}

/**
 * Set theme color to storage
 * @param {string} color - Theme color
 */
export function setThemeColor(color) {
  localStg.set(THEME_COLOR_KEY, color);
}

/**
 * Get dark mode from storage
 * @returns {boolean} Dark mode state
 */
export function getDarkMode() {
  return localStg.get(DARK_MODE_KEY) || false;
}

/**
 * Set dark mode to storage
 * @param {boolean} isDark - Dark mode state
 */
export function setDarkMode(isDark) {
  localStg.set(DARK_MODE_KEY, isDark);
}

/**
 * Get theme settings from storage
 * @returns {Object|null} Theme settings
 */
export function getThemeSettings() {
  return localStg.get(THEME_SETTINGS_KEY);
}

/**
 * Set theme settings to storage
 * @param {Object} settings - Theme settings
 */
export function setThemeSettings(settings) {
  localStg.set(THEME_SETTINGS_KEY, settings);
}

/**
 * Clear theme storage
 */
export function clearThemeStorage() {
  localStg.remove(THEME_COLOR_KEY);
  localStg.remove(DARK_MODE_KEY);
  localStg.remove(THEME_SETTINGS_KEY);
}

/**
 * Get system preferred color scheme
 * @returns {string} 'dark' or 'light'
 */
export function getSystemColorScheme() {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light';
}

/**
 * Check if system supports color scheme preference
 * @returns {boolean} True if supported
 */
export function supportsColorSchemePreference() {
  return typeof window !== 'undefined' && window.matchMedia && window.matchMedia('(prefers-color-scheme)').media !== 'not all';
}

/**
 * Create theme watcher for system color scheme changes
 * @param {Function} callback - Callback function
 * @returns {Function} Cleanup function
 */
export function createSystemThemeWatcher(callback) {
  if (!supportsColorSchemePreference()) {
    return () => {};
  }

  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  const handler = (e) => callback(e.matches ? 'dark' : 'light');
  
  mediaQuery.addEventListener('change', handler);
  
  return () => mediaQuery.removeEventListener('change', handler);
}
