<script setup>
import { useRouter } from 'vue-router';
import { useAppStore } from '@/store/modules/appStore';
import { useThemeStore } from '@/store/modules/themeStore';
import { setupMixMenuContext, useMenu } from '../../../context';
import FirstLevelMenu from '../components/first-level-menu.vue';

defineOptions({
  name: 'HorizontalMixMenu'
});

const router = useRouter();
const appStore = useAppStore();
const themeStore = useThemeStore();

const { allMenus, childLevelMenus, activeFirstLevelMenuKey, setActiveFirstLevelMenuKey } = setupMixMenuContext();
const { selectedKey } = useMenu();

function handleSelectMixMenu(menu) {
  setActiveFirstLevelMenuKey(menu.key);

  if (!menu.children?.length) {
    router.push(menu.routeKey);
  }
}

function handleMenuSelect(key) {
  router.push(key);
}
</script>

<template>
  <!-- 水平菜单放在头部 -->
  <div id="global-header-menu" class="flex-1">
    <n-menu
      mode="horizontal"
      :value="selectedKey"
      :options="childLevelMenus"
      :indent="18"
      responsive
      @update:value="handleMenuSelect"
    />
  </div>
  
  <!-- 第一级菜单放在侧边栏 -->
  <div id="global-sider-menu" class="h-full">
    <FirstLevelMenu
      :menus="allMenus"
      :active-menu-key="activeFirstLevelMenuKey"
      :sider-collapse="appStore.siderCollapse"
      :dark-mode="themeStore.darkMode"
      :theme-color="themeStore.themeColor"
      @select="handleSelectMixMenu"
      @toggle-sider-collapse="appStore.toggleSiderCollapse"
    />
  </div>
</template>

<style scoped></style>
