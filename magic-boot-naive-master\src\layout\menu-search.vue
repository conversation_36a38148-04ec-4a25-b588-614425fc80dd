<template>
    <div class="menu-search">
        <!-- 收起状态：只显示搜索图标 -->
        <div v-if="!expanded" class="search-icon" @click="expandSearch">
            <mb-icon icon="search" size="1.2em" color="white" />
        </div>
        
        <!-- 展开状态：显示完整搜索框 -->
        <div v-if="expanded" class="search-box" :class="{ active: searchFocused }">
            <mb-icon icon="search" size="1.2em" color="white" @click="collapseSearch" style="cursor: pointer;" />
            <input 
                ref="searchInput"
                v-model="searchKeyword" 
                @focus="handleSearchFocus"
                @blur="handleSearchBlur"
                @keydown="handleSearchKeydown"
                placeholder="搜索菜单..." 
                type="text"
            />
        </div>
        
        <!-- 搜索结果 -->
        <div v-if="showSearchResult" class="search-results">
            <div 
                v-for="(result, index) in searchResults" 
                :key="result.id"
                :ref="el => setSearchItemRef(el, index)"
                :class="['search-item', { active: selectedIndex === index }]"
                @click="jumpToSearchResult(result)"
                @mouseenter="selectedIndex = index"
            >
                <div class="item-header">
                    <mb-icon :icon="result.icon || 'DocumentOutline'" size="1em" />
                    <span class="item-title" v-html="highlightKeyword(result.title, searchKeyword)"></span>
                </div>
                <div class="item-path" v-html="highlightKeyword(result.breadcrumb, searchKeyword)"></div>
                <div class="item-route" v-html="highlightKeyword(result.path, searchKeyword)"></div>
            </div>
            <div v-if="searchResults.length === 0" class="no-result">
                暂无匹配的菜单
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { pinyin } from 'pinyin-pro'
import router from '@/scripts/router'
import common from '@/scripts/common'

// 接收props
const props = defineProps({
    menuOptions: {
        type: Array,
        required: true
    }
})

// 搜索相关数据
const searchKeyword = ref('')
const searchFocused = ref(false)
const selectedIndex = ref(0)
const searchInput = ref()
const expanded = ref(false) // 搜索框展开状态

// 搜索结果项的引用
const searchItemRefs = ref([])

// 提取所有可跳转的菜单项
const allMenuItems = ref([])

// 搜索结果计算属性
const searchResults = computed(() => {
    if (!searchKeyword.value.trim()) {
        return []
    }
    
    const keyword = searchKeyword.value.toLowerCase().trim()
    
    return allMenuItems.value.filter(item => {
        // 标题模糊匹配
        const titleMatch = item.title.toLowerCase().includes(keyword)
        
        // 拼音全拼匹配
        const pinyinMatch = item.pinyin.toLowerCase().includes(keyword)
        
        // 拼音首字母匹配
        const firstLetterMatch = item.firstLetter.toLowerCase().includes(keyword)
        
        // 面包屑路径匹配
        const breadcrumbMatch = item.breadcrumb.toLowerCase().includes(keyword)
        
        // 层级独立匹配 - 支持中文、拼音、简拼三种匹配方式
        const levelMatch = item.breadcrumbLevelsData.some(levelData => {
            // 中文模糊匹配
            const chineseMatch = levelData.name.toLowerCase().includes(keyword)
            
            // 拼音匹配
            const pinyinMatch = levelData.pinyin.includes(keyword)
            
            // 简拼匹配
            const firstLetterMatch = levelData.firstLetter.includes(keyword)
            
            return chineseMatch || pinyinMatch || firstLetterMatch
        })
        
        // 访问路径匹配
        const pathMatch = item.path.toLowerCase().includes(keyword)
        
        return titleMatch || pinyinMatch || firstLetterMatch || breadcrumbMatch || levelMatch || pathMatch
    }).slice(0, 8) // 最多显示8个结果
})

// 是否显示搜索结果
const showSearchResult = computed(() => {
    return searchFocused.value && searchKeyword.value.trim().length > 0
})

// 提取所有可跳转的菜单项
function extractMenuItems(menuList, breadcrumbs = []) {
    menuList.forEach(menu => {
        const currentBreadcrumbs = [...breadcrumbs, menu.title]
        
        if (menu.children && menu.children.length > 0) {
            extractMenuItems(menu.children, currentBreadcrumbs)
        } else if (menu.path && menu.title) {
            // 这是最终可跳转的菜单项
            // 使用pinyin库生成拼音数据
            const pinyinResult = pinyin(menu.title, { toneType: 'none' })
            const firstLetterResult = pinyin(menu.title, { pattern: 'first', toneType: 'none' })
            
            // 为每个层级名称生成拼音和简拼数据
            const breadcrumbLevelsData = currentBreadcrumbs.map(levelName => {
                try {
                    const levelPinyin = pinyin(levelName, { toneType: 'none' }).replace(/\s+/g, '')
                    const levelFirstLetter = pinyin(levelName, { pattern: 'first', toneType: 'none' }).replace(/\s+/g, '')
                    return {
                        name: levelName,
                        pinyin: levelPinyin.toLowerCase(),
                        firstLetter: levelFirstLetter.toLowerCase()
                    }
                } catch (e) {
                    return {
                        name: levelName,
                        pinyin: '',
                        firstLetter: ''
                    }
                }
            })

            const menuItem = {
                id: menu.id,
                title: menu.title,
                path: menu.path,
                icon: menu.icon,
                openMode: menu.openMode,
                breadcrumb: currentBreadcrumbs.join(' > '),
                breadcrumbLevels: currentBreadcrumbs, // 保存所有层级名称数组
                breadcrumbLevelsData: breadcrumbLevelsData, // 保存层级拼音数据
                pinyin: pinyinResult.replace(/\s+/g, ''), // 移除空格，如：caidanguanli
                firstLetter: firstLetterResult.replace(/\s+/g, '') // 移除空格，如：cdgl
            }
            allMenuItems.value.push(menuItem)
        }
    })
}

// 初始化菜单项
function initMenuItems() {
    allMenuItems.value = []
    if (props.menuOptions && props.menuOptions.length > 0) {
        extractMenuItems(props.menuOptions)
    }
}

// 展开/收起方法
function expandSearch() {
    expanded.value = true
    // 展开后自动聚焦到搜索框
    nextTick(() => {
        searchInput.value?.focus()
    })
}

function collapseSearch() {
    expanded.value = false
    searchKeyword.value = ''
    searchFocused.value = false
}

// 搜索相关方法
function handleSearchFocus() {
    searchFocused.value = true
}

function handleSearchBlur() {
    // 延迟隐藏搜索结果，以便点击搜索结果项或图标
    setTimeout(() => {
        searchFocused.value = false
        // 如果搜索框为空，自动收起
        if (!searchKeyword.value.trim()) {
            expanded.value = false
        }
    }, 200)
}

function handleSearchKeydown(event) {
    if (!showSearchResult.value) return
    
    const resultsLength = searchResults.value.length
    if (resultsLength === 0) return
    
    switch (event.key) {
        case 'ArrowDown':
            event.preventDefault()
            selectedIndex.value = (selectedIndex.value + 1) % resultsLength
            scrollToSelectedItem()
            break
        case 'ArrowUp':
            event.preventDefault()
            selectedIndex.value = selectedIndex.value === 0 ? resultsLength - 1 : selectedIndex.value - 1
            scrollToSelectedItem()
            break
        case 'Enter':
            event.preventDefault()
            if (searchResults.value[selectedIndex.value]) {
                jumpToSearchResult(searchResults.value[selectedIndex.value])
            }
            break
        case 'Escape':
            collapseSearch()
            break
    }
}

function jumpToSearchResult(menuItem) {
    // 清空搜索框和状态
    searchKeyword.value = ''
    searchFocused.value = false
    expanded.value = false
    searchInput.value.blur()
    
    // 跳转路由
    if (menuItem.openMode === '1') {
        window.open(common.handlerUrlPage(menuItem.path))
    } else {
        router.push({
            path: menuItem.path
        })
    }
}

// 监听菜单选项变化，重新初始化
watch(() => props.menuOptions, () => {
    initMenuItems()
}, { immediate: true })

// 监听搜索结果变化，重置选中索引
watch(searchResults, () => {
    selectedIndex.value = 0
    // 清空refs数组
    searchItemRefs.value = []
})

// 设置搜索结果项的ref
function setSearchItemRef(el, index) {
    if (el) {
        searchItemRefs.value[index] = el
    }
}

// 滚动到选中项
function scrollToSelectedItem() {
    nextTick(() => {
        const selectedItem = searchItemRefs.value[selectedIndex.value]
        if (selectedItem) {
            selectedItem.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest',
                inline: 'nearest'
            })
        }
    })
}

/**
 * 高亮关键词函数
 * 支持中文直接匹配、拼音匹配、简拼匹配的智能高亮
 * @param {string} text - 待高亮的文本
 * @param {string} keyword - 搜索关键词
 * @returns {string} - 包含高亮标签的HTML字符串
 */
function highlightKeyword(text, keyword) {
    if (!keyword || !text) {
        return text
    }
    
    const keywordLower = keyword.toLowerCase().trim()
    
    // 如果是中文关键词，直接进行匹配
    if (/[\u4e00-\u9fa5]/.test(keywordLower)) {
        const regex = new RegExp(`(${keywordLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
        return text.replace(regex, '<mark>$1</mark>')
    }
    
    // 如果是英文关键词，首先尝试直接匹配
    const directMatch = new RegExp(`(${keywordLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    if (directMatch.test(text)) {
        return text.replace(directMatch, '<mark>$1</mark>')
    }
    
    // 英文关键词进行拼音和简拼匹配
    let highlightedText = text
    let hasHighlight = false
    
    // 逐字符检查，找到匹配拼音或简拼的中文字符
    for (let i = 0; i < text.length; i++) {
        const char = text.charAt(i)
        if (/[\u4e00-\u9fa5]/.test(char)) { // 只处理中文字符
            try {
                const charPinyin = pinyin(char, { toneType: 'none' }).toLowerCase()
                const charFirstLetter = pinyin(char, { pattern: 'first', toneType: 'none' }).toLowerCase()
                
                // 检查关键词是否匹配该字符的拼音或简拼
                if (charPinyin === keywordLower || charFirstLetter === keywordLower) {
                    // 高亮该字符
                    if (!hasHighlight) {
                        highlightedText = text.substring(0, i) + '<mark>' + char + '</mark>' + text.substring(i + 1)
                        hasHighlight = true
                        break // 只高亮第一个匹配的字符
                    }
                }
            } catch (e) {
                // 如果拼音转换出错，跳过该字符
                continue
            }
        }
    }
    
    // 如果单字符没有匹配，尝试多字符匹配
    if (!hasHighlight) {
        try {
            // 检查整个文本的拼音或简拼是否包含关键词
            const textPinyin = pinyin(text, { toneType: 'none' }).replace(/\s+/g, '').toLowerCase()
            const textFirstLetter = pinyin(text, { pattern: 'first', toneType: 'none' }).replace(/\s+/g, '').toLowerCase()
            
            if (textPinyin.includes(keywordLower) || textFirstLetter.includes(keywordLower)) {
                // 尝试找到匹配的字符序列进行精确高亮
                let matchStart = -1
                let matchLength = 0
                
                // 检查连续字符是否匹配关键词
                for (let i = 0; i <= text.length - keywordLower.length; i++) {
                    const subText = text.substring(i, i + keywordLower.length)
                    if (/[\u4e00-\u9fa5]/.test(subText)) {
                        const subPinyin = pinyin(subText, { toneType: 'none' }).replace(/\s+/g, '').toLowerCase()
                        const subFirstLetter = pinyin(subText, { pattern: 'first', toneType: 'none' }).replace(/\s+/g, '').toLowerCase()
                        
                        if (subPinyin === keywordLower || subFirstLetter === keywordLower) {
                            matchStart = i
                            matchLength = keywordLower.length
                            break
                        }
                    }
                }
                
                if (matchStart >= 0) {
                    // 精确高亮匹配的字符序列
                    const beforeMatch = text.substring(0, matchStart)
                    const matchText = text.substring(matchStart, matchStart + matchLength)
                    const afterMatch = text.substring(matchStart + matchLength)
                    highlightedText = beforeMatch + '<mark>' + matchText + '</mark>' + afterMatch
                }
            }
        } catch (e) {
            // 如果拼音转换出错，返回原文本
            return text
        }
    }
    
    return highlightedText
}
</script>

<style lang="less" scoped>
.menu-search{
    position: relative;
    margin-left: 20px;
    .search-icon{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 32px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        &:hover{
            background: rgba(255, 255, 255, 0.1);
        }
    }
    .search-box{
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 6px;
        padding: 5px 12px;
        min-width: 220px;
        transition: all 0.3s ease;
        &:hover, &.active{
            background: rgba(255, 255, 255, 0.2);
        }
        input{
            background: none;
            border: none;
            outline: none;
            color: white;
            font-size: 12px;
            margin-left: 8px;
            flex: 1;
            &::placeholder{
                color: rgba(255, 255, 255, 0.7);
            }
        }
    }
    .search-results{
        position: absolute;
        top: 40px;
        left: 0;
        right: 0;
        background: white;
        border-radius: 6px;
        box-shadow: 0 4px 20px rgba(0,0,0,.15);
        z-index: 99999;
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e0e0e0;
        animation: slideDown 0.2s ease-out;
        .search-item{
            padding: 15px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f5f5f5;
            transition: background-color 0.2s ease;
            .item-header{
                display: flex;
                align-items: flex-start;
                margin-bottom: 6px;
                .item-title{
                    margin-left: 10px;
                    font-size: 13px;
                    color: #333;
                    font-weight: 500;
                    line-height: 1.4;
                    word-break: break-all;
                    :deep(mark) {
                        background-color: #ffeb3b;
                        color: #333;
                        padding: 1px 2px;
                        border-radius: 2px;
                        font-weight: 600;
                    }
                }
            }
            .item-path{
                margin-left: 25px;
                font-size: 11px;
                color: #999;
                line-height: 1.3;
                margin-bottom: 4px;
                word-break: break-all;
                :deep(mark) {
                    background-color: #ffeb3b;
                    color: #333;
                    padding: 1px 2px;
                    border-radius: 2px;
                    font-weight: 600;
                }
            }
            .item-route{
                margin-left: 25px;
                font-size: 10px;
                color: #bbb;
                line-height: 1.3;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                word-break: break-all;
                :deep(mark) {
                    background-color: #ffeb3b;
                    color: #333;
                    padding: 1px 2px;
                    border-radius: 2px;
                    font-weight: 600;
                }
            }
            &:hover, &.active{
                background: #f0f8ff;
                .item-header .item-title{
                    color: #0869bd;
                    :deep(mark) {
                        background-color: #ffc107;
                        color: #333;
                    }
                }
                .item-path{
                    :deep(mark) {
                        background-color: #ffc107;
                        color: #333;
                    }
                }
                .item-route{
                    :deep(mark) {
                        background-color: #ffc107;
                        color: #333;
                    }
                }
            }
            &:last-child{
                border-bottom: none;
            }
        }
        .no-result{
            padding: 20px 15px;
            text-align: center;
            color: #999;
            font-size: 13px;
        }
    }
}

// 搜索结果展开动画
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style> 