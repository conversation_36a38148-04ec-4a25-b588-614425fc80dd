<script setup>
import { computed } from 'vue';
import { useThemeStore } from '@/store/modules/themeStore';
import { useAppStore } from '@/store/modules/appStore';
import GlobalBreadcrumb from './global-breadcrumb/index.vue';
import MenuSearch from './menu-search/index.vue';
import Men<PERSON><PERSON>oggler from './global-header/components/menu-toggler.vue';
import FullscreenButton from './global-header/components/fullscreen-button.vue';
import ThemeButton from './global-header/components/theme-button.vue';
import UserAvatar from './global-header/components/user-avatar.vue';

defineOptions({
  name: 'GlobalHeader'
});

const themeStore = useThemeStore();
const appStore = useAppStore();

const showLogo = computed(() => {
  return themeStore.layout.mode === 'horizontal' ||
    themeStore.layout.mode === 'horizontal-mix' ||
    (themeStore.layout.mode === 'vertical' && appStore.isMobile);
});

const showMenu = computed(() => {
  return themeStore.layout.mode === 'horizontal' ||
    themeStore.layout.mode === 'horizontal-mix';
});

const showMenuToggler = computed(() => {
  return themeStore.layout.mode === 'vertical' ||
    themeStore.layout.mode === 'vertical-mix';
});


</script>

<template>
  <div class="h-full flex items-center px-3 shadow-header bg-container">
    <!-- Logo -->
    <div v-if="showLogo" class="logo-box h-full flex items-center" :style="{ width: themeStore.sider.width + 'px' }">
      <span class="text-xl font-bold text-primary">{{ $global?.title || 'MagicBoot' }}</span>
    </div>

    <!-- Menu Toggler -->
    <MenuToggler v-if="showMenuToggler" :collapsed="appStore.siderCollapse" class="mr-3"
      @click="appStore.toggleSiderCollapse" />

    <!-- Header Menu -->
    <div v-if="showMenu" id="global-header-menu" class="flex-1 h-full flex items-center">
      <!-- 水平菜单将通过 Teleport 渲染到这里 -->
    </div>

    <!-- Header Content -->
    <div class="flex-1 h-full flex items-center" :class="{ 'flex-none': showMenu }">
      <!-- Breadcrumb -->
      <GlobalBreadcrumb v-if="!appStore.isMobile && !showMenu" class="ml-3" />
    </div>

    <!-- Header Right -->
    <div class="h-full flex items-center justify-end gap-2">
      <!-- Search -->
      <MenuSearch v-if="themeStore.header.globalSearch.visible" />

      <!-- Full Screen -->
      <FullscreenButton v-if="!appStore.isMobile" />

      <!-- Theme Toggle -->
      <ThemeButton />

      <!-- User Info -->
      <UserAvatar />
    </div>
  </div>
</template>



<style scoped>
.shadow-header {
  box-shadow: var(--header-box-shadow, 0 1px 2px rgb(0, 21, 41, 0.08));
}

.bg-container {
  background-color: var(--container-color, rgb(255, 255, 255));
}

.text-primary {
  color: var(--primary-color, #646cff);
}

.menu-toggler {
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}
</style>
