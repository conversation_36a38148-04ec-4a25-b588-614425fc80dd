import type { ChildContext, FileExtension, PluginSettings, RuleContext } from '../types.js';
export declare function getFileExtensions(settings: PluginSettings): Set<`.${string}`>;
export declare function ignore(filepath: string, context: ChildContext | RuleContext, skipExtensionCheck?: boolean): boolean;
export declare function hasValidExtension(filepath: string, context: ChildContext | RuleContext): filepath is `${string}${FileExtension}`;
