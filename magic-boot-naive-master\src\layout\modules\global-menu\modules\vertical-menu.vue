<script setup>
import { ref, computed, watch, h } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/store/modules/userStore';
import { useThemeStore } from '@/store/modules/themeStore';
import { useAppStore } from '@/store/modules/appStore';
import MbIcon from '@/components/magic/basic/mb-icon.vue';

defineOptions({
  name: 'VerticalMenu'
});

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const themeStore = useThemeStore();
const appStore = useAppStore();

const selectedKey = ref(route.path);
const expandedKeys = ref([]);

// 处理菜单数据
const menuOptions = computed(() => {
  const menus = userStore.getPermissionRouters || [];
  return processMenus(menus);
});

function processMenus(menus) {
  return menus.map(menu => ({
    label: menu.title,
    key: menu.path,
    icon: menu.icon ? () => h(MbIcon, { icon: menu.icon, size: '18px' }) : undefined,
    children: menu.children ? processMenus(menu.children) : undefined
  }));
}

function handleMenuSelect(key) {
  selectedKey.value = key;
  router.push(key);

  // 移动端选择菜单后收起侧边栏
  if (appStore.isMobile) {
    appStore.toggleSiderCollapse();
  }
}

function updateExpandedKeys() {
  if (appStore.siderCollapse || !selectedKey.value) {
    expandedKeys.value = [];
    return;
  }

  // 智能展开逻辑：根据当前路由展开对应的菜单项
  const currentPath = selectedKey.value;
  const pathSegments = currentPath.split('/').filter(Boolean);
  const expandKeys = [];

  // 构建展开路径
  let buildPath = '';
  for (const segment of pathSegments) {
    buildPath += '/' + segment;
    expandKeys.push(buildPath);
  }

  expandedKeys.value = expandKeys;
}

// 监听路由变化更新选中状态和展开状态
watch(
  () => route.path,
  (newPath) => {
    selectedKey.value = newPath;
    updateExpandedKeys();
  },
  { immediate: true }
);

// 监听侧边栏折叠状态
watch(
  () => appStore.siderCollapse,
  () => {
    updateExpandedKeys();
  }
);
</script>

<template>
  <div class="global-menu h-full">
    <n-scrollbar class="h-full">
      <n-menu v-model:value="selectedKey" v-model:expanded-keys="expandedKeys" :options="menuOptions"
        :collapsed="appStore.siderCollapse" :collapsed-width="themeStore.sider.collapsedWidth" :collapsed-icon-size="22"
        :inverted="themeStore.sider.inverted" :indent="18" mode="vertical" @update:value="handleMenuSelect" />
    </n-scrollbar>
  </div>
</template>

<style scoped>
.global-menu {
  height: 100%;
}

.h-full {
  height: 100%;
}
</style>
