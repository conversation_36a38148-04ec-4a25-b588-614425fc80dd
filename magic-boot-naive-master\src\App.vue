<template>
    <n-config-provider :locale="zhCN" :date-locale="dateZhCN" :theme="naiveDarkTheme"
        :theme-overrides="themeStore.naiveTheme">
        <n-message-provider>
            <n-dialog-provider>
                <n-loading-bar-provider>
                    <router-view />
                </n-loading-bar-provider>
            </n-dialog-provider>
        </n-message-provider>
    </n-config-provider>
</template>

<script setup>
import { computed } from 'vue'
import { NConfigProvider, zhCN, dateZhCN, darkTheme } from 'naive-ui'
import { useThemeStore } from '@/store/modules/themeStore'

const themeStore = useThemeStore()

// 正确配置 naive-ui 主题
const naiveDarkTheme = computed(() => (themeStore.darkMode ? darkTheme : undefined))

</script>

<style lang="less">
@import 'styles/index.less';

.n-config-provider {
    height: 100%;
}
</style>
