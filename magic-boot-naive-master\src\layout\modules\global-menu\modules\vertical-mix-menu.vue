<script setup>
import { computed, ref, watch, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useThemeStore } from '@/store/modules/themeStore';
import { useAppStore } from '@/store/modules/appStore';
import { setupMixMenuContext, useMenu } from '../../../context';
import FirstLevelMenu from '../components/first-level-menu.vue';
import MbIcon from '@/components/magic/basic/mb-icon.vue';

defineOptions({
  name: 'VerticalMixMenu'
});

const route = useRoute();
const router = useRouter();
const themeStore = useThemeStore();
const appStore = useAppStore();

const {
  allMenus,
  childLevelMenus,
  activeFirstLevelMenuKey,
  setActiveFirstLevelMenuKey
} = setupMixMenuContext();

const { selectedKey } = useMenu();

const inverted = computed(() => !themeStore.darkMode && themeStore.sider.inverted);

const hasChildMenus = computed(() => childLevelMenus.value.length > 0);

// 抽屉显示状态
const drawerVisible = ref(false);

// 子菜单固定状态
const mixSiderFixed = ref(false);

// 显示抽屉的条件
const showDrawer = computed(() => hasChildMenus.value && (drawerVisible.value || mixSiderFixed.value));

function handleSelectMixMenu(menu) {
  setActiveFirstLevelMenuKey(menu.key);

  if (menu.children?.length) {
    // 有子菜单，显示抽屉
    drawerVisible.value = true;
  } else {
    router.push(menu.routeKey);
  }
}

function handleResetActiveMenu() {
  drawerVisible.value = false;

  if (!mixSiderFixed.value) {
    // 如果没有固定，重置激活菜单
    setActiveFirstLevelMenuKey('');
  }
}

function toggleMixSiderFixed() {
  mixSiderFixed.value = !mixSiderFixed.value;
}

function handleMenuSelect(key) {
  router.push(key);
}

const expandedKeys = ref([]);

function updateExpandedKeys() {
  if (appStore.siderCollapse || !selectedKey.value) {
    expandedKeys.value = [];
    return;
  }
  // 简单的展开逻辑，可以根据需要优化
  expandedKeys.value = [];
}

watch(
  () => route.name,
  () => {
    updateExpandedKeys();
  },
  { immediate: true }
);
</script>

<template>
  <div id="global-sider-menu" class="h-full flex" @mouseleave="handleResetActiveMenu">
    <FirstLevelMenu :menus="allMenus" :active-menu-key="activeFirstLevelMenuKey" :inverted="inverted"
      :sider-collapse="appStore.siderCollapse" :dark-mode="themeStore.darkMode" :theme-color="themeStore.themeColor"
      @select="handleSelectMixMenu" @toggle-sider-collapse="appStore.toggleSiderCollapse" />
    <div class="relative h-full transition-width-300"
      :style="{ width: mixSiderFixed && hasChildMenus ? themeStore.sider.mixChildMenuWidth + 'px' : '0px' }">
      <div class="absolute left-0 top-0 h-full flex flex-col shadow-sm transition-all-300 bg-container"
        :class="{ 'mix-drawer-inverted': inverted }"
        :style="{ width: showDrawer ? themeStore.sider.mixChildMenuWidth + 'px' : '0px' }">
        <header class="flex items-center justify-between px-3" :style="{ height: themeStore.header.height + 'px' }">
          <h2 class="text-base font-bold" :class="{ 'text-white': inverted, 'text-primary': !inverted }">子菜单</h2>
          <n-button quaternary size="small" :class="{ 'text-white hover:text-white': inverted }"
            @click="toggleMixSiderFixed">
            <template #icon>
              <n-icon size="16">
                <svg viewBox="0 0 24 24">
                  <path v-if="mixSiderFixed" fill="currentColor"
                    d="M16,12V4H17V2C17,1.45 16.55,1 16,1H8C7.45,1 7,1.45 7,2V4H8V12L6,10V20A2,2 0 0,0 8,22H16A2,2 0 0,0 18,20V10L16,12Z" />
                  <path v-else fill="currentColor"
                    d="M16,12V4H17V2C17,1.45 16.55,1 16,1H8C7.45,1 7,1.45 7,2V4H8V12L6,10V20A2,2 0 0,0 8,22H16A2,2 0 0,0 18,20V10L16,12M14,4V12L12,10L10,12V4H14Z" />
                </svg>
              </n-icon>
            </template>
          </n-button>
        </header>
        <n-scrollbar class="flex-1">
          <n-menu v-model:expanded-keys="expandedKeys" mode="vertical" :value="selectedKey" :options="childLevelMenus"
            :inverted="inverted" :indent="18" @update:value="handleMenuSelect" />
        </n-scrollbar>
      </div>
    </div>
  </div>
</template>

<style scoped>
.transition-width-300 {
  transition: width 0.3s ease;
}

.mix-drawer-inverted {
  background-color: var(--inverted-color, rgb(0, 20, 40));
}

.bg-container {
  background-color: var(--container-bg-color, #ffffff);
}
</style>
