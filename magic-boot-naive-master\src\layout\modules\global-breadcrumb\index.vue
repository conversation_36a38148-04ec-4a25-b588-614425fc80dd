<script setup>
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useThemeStore } from '@/store/modules/themeStore';
import { useUserStore } from '@/store/modules/userStore';
import MbIcon from '@/components/magic/basic/mb-icon.vue';

defineOptions({
  name: 'GlobalBreadcrumb'
});

const route = useRoute();
const router = useRouter();
const themeStore = useThemeStore();
const userStore = useUserStore();

// 生成面包屑数据
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title);
  const breadcrumbList = [];

  // 添加首页
  if (route.path !== '/home') {
    breadcrumbList.push({
      title: '首页',
      path: '/home',
      icon: 'HomeOutline'
    });
  }

  // 添加当前路由的面包屑
  matched.forEach((item, index) => {
    if (item.path !== '/home') {
      breadcrumbList.push({
        title: item.meta.title,
        path: item.path,
        icon: item.meta.icon,
        isLast: index === matched.length - 1
      });
    }
  });

  return breadcrumbList;
});

// 处理面包屑点击
function handleBreadcrumbClick(breadcrumb) {
  if (!breadcrumb.isLast && breadcrumb.path) {
    router.push(breadcrumb.path);
  }
}

// 获取下拉菜单选项
function getDropdownOptions(breadcrumb) {
  // 这里可以根据需要添加子菜单逻辑
  const menus = userStore.getPermissionRouters || [];
  const currentMenu = findMenuByPath(menus, breadcrumb.path);
  
  if (currentMenu && currentMenu.children && currentMenu.children.length > 0) {
    return currentMenu.children.map(child => ({
      label: child.title,
      key: child.path,
      icon: child.icon
    }));
  }
  
  return [];
}

// 查找菜单项
function findMenuByPath(menus, path) {
  for (const menu of menus) {
    if (menu.path === path) {
      return menu;
    }
    if (menu.children) {
      const found = findMenuByPath(menu.children, path);
      if (found) return found;
    }
  }
  return null;
}

// 处理下拉菜单选择
function handleDropdownSelect(key) {
  router.push(key);
}
</script>

<template>
  <n-breadcrumb v-if="themeStore.header.breadcrumb.visible" class="global-breadcrumb">
    <n-breadcrumb-item
      v-for="(breadcrumb, index) in breadcrumbs"
      :key="breadcrumb.path || index"
    >
      <div
        class="breadcrumb-content flex items-center"
        :class="{ 'cursor-pointer': !breadcrumb.isLast }"
        @click="handleBreadcrumbClick(breadcrumb)"
      >
        <!-- 图标 -->
        <MbIcon
          v-if="breadcrumb.icon && themeStore.header.breadcrumb.showIcon"
          :icon="breadcrumb.icon"
          size="16px"
          class="mr-1"
        />
        
        <!-- 标题 -->
        <span>{{ breadcrumb.title }}</span>
        
        <!-- 下拉菜单 -->
        <n-dropdown
          v-if="getDropdownOptions(breadcrumb).length > 0"
          :options="getDropdownOptions(breadcrumb)"
          @select="handleDropdownSelect"
        >
          <n-icon size="12" class="ml-1 cursor-pointer">
            <svg viewBox="0 0 24 24">
              <path fill="currentColor" d="M7 10l5 5 5-5z"/>
            </svg>
          </n-icon>
        </n-dropdown>
      </div>
    </n-breadcrumb-item>
  </n-breadcrumb>
</template>

<style scoped>
.global-breadcrumb {
  font-size: 14px;
}

.breadcrumb-content {
  transition: color 0.2s ease;
}

.breadcrumb-content:hover:not(.cursor-pointer) {
  color: var(--n-text-color-hover);
}

.breadcrumb-content.cursor-pointer:hover {
  color: var(--n-color-target);
}
</style>
