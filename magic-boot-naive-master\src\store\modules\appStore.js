import { ref, nextTick } from 'vue';
import { defineStore } from 'pinia';
import { useEventListener } from '@vueuse/core';
import { useThemeStore } from './themeStore';
import {
  checkIsMobile,
  saveThemeBackup,
  getThemeBackup,
  removeThemeBackup
} from './app/shared';

export const useAppStore = defineStore('app', () => {
  /** Is mobile layout */
  const isMobile = ref(false);
  
  /** Sider collapse status */
  const siderCollapse = ref(false);
  
  /** Full content mode */
  const fullContent = ref(false);
  
  /** Content x scrollable */
  const contentXScrollable = ref(false);
  
  /** Reload flag */
  const reloadFlag = ref(true);

  /**
   * Check if is mobile and handle theme backup
   */
  function checkMobile() {
    const newIsMobile = checkIsMobile();

    if (newIsMobile !== isMobile.value) {
      if (newIsMobile) {
        // Backup current theme settings before switching to mobile
        const themeStore = useThemeStore();
        saveThemeBackup({
          layout: themeStore.layout.mode,
          siderCollapse: siderCollapse.value
        });

        // Auto collapse sider on mobile
        siderCollapse.value = true;
      } else {
        // Restore theme settings when switching back from mobile
        const backup = getThemeBackup();
        if (backup) {
          nextTick(() => {
            const themeStore = useThemeStore();
            themeStore.setThemeLayout(backup.layout);
            siderCollapse.value = backup.siderCollapse;
            removeThemeBackup();
          });
        }
      }
    }

    isMobile.value = newIsMobile;
  }

  /**
   * Toggle sider collapse
   */
  function toggleSiderCollapse() {
    siderCollapse.value = !siderCollapse.value;
  }

  /**
   * Set sider collapse
   * @param {boolean} collapse - Collapse status
   */
  function setSiderCollapse(collapse) {
    siderCollapse.value = collapse;
  }

  /**
   * Toggle full content
   */
  function toggleFullContent() {
    fullContent.value = !fullContent.value;
  }

  /**
   * Set full content
   * @param {boolean} full - Full content status
   */
  function setFullContent(full) {
    fullContent.value = full;
  }

  /**
   * Set content x scrollable
   * @param {boolean} scrollable - Scrollable status
   */
  function setContentXScrollable(scrollable) {
    contentXScrollable.value = scrollable;
  }

  /**
   * Reload page
   */
  async function reloadPage() {
    reloadFlag.value = false;
    await nextTick();
    reloadFlag.value = true;
  }

  // Initialize mobile check
  checkMobile();

  // Listen to window resize
  useEventListener(window, 'resize', checkMobile);

  return {
    isMobile,
    siderCollapse,
    fullContent,
    contentXScrollable,
    reloadFlag,
    checkMobile,
    toggleSiderCollapse,
    setSiderCollapse,
    toggleFullContent,
    setFullContent,
    setContentXScrollable,
    reloadPage
  };
});
