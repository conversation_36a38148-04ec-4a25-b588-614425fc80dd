@tailwind base;
@tailwind components;
@tailwind utilities;

@import './nprogress.css';
@import './transition.css';

/* Custom base styles */
@layer base {

  html,
  body,
  #app {
    height: 100%;
  }

  html {
    overflow-x: hidden;
    color: rgb(var(--base-text-color));
  }

  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  * {
    box-sizing: border-box;
  }
}

/* Custom component styles */
@layer components {
  .bg-container {
    background-color: rgb(var(--container-bg-color));
  }

  .bg-layout {
    background-color: rgb(var(--layout-bg-color));
  }

  .bg-inverted {
    background-color: rgb(var(--inverted-bg-color));
  }

  .text-primary {
    color: rgb(var(--primary-color));
  }

  .text-base {
    color: rgb(var(--base-text-color));
  }

  .shadow-header {
    box-shadow: var(--header-box-shadow);
  }

  .shadow-sider {
    box-shadow: var(--sider-box-shadow);
  }

  .shadow-tab {
    box-shadow: var(--tab-box-shadow);
  }
}

/* Custom utility styles */
@layer utilities {
  .transition-base {
    transition: all 0.3s ease-in-out;
  }

  .transition-width-300 {
    transition: width 0.3s ease;
  }

  .card-wrapper {
    @apply rounded-lg shadow-sm;
  }

  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-x-center {
    @apply flex justify-center;
  }

  .flex-y-center {
    @apply flex items-center;
  }
}