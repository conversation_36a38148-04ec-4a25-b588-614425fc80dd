<template>
  <div class="mb-x6design-preview">
    <div class="mb-x6design-preview-header">
      <div class="mb-x6design-preview-title">{{ title || '图形预览' }}</div>
    </div>
    <div class="mb-x6design-preview-content">
      <div class="mb-x6design-preview-graph" ref="graphContainer"></div>
      <div v-if="loading" class="mb-x6design-preview-loading">
        <n-spin size="large" />
      </div>
      <div v-if="!loading && !hasData" class="mb-x6design-preview-empty">
        <p>{{ emptyText || '暂无预览数据' }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Graph } from '@antv/x6'
import { NSpin } from 'naive-ui'
import { initGraph } from './utils/graph-config'
import { transformJSONToGraphData, isValidGraphData } from './utils/data-transform'

export default {
  name: 'mb-x6design-preview',
  components: {
    NSpin
  },
  props: {
    graphData: {
      type: [Object, String],
      default: null
    },
    routeConfig: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    emptyText: {
      type: String,
      default: ''
    },
    clickable: {
      type: Boolean,
      default: true
    }
  },
  emits: ['node-click'],
  setup(props, { emit }) {
    const router = useRouter()
    const graphContainer = ref(null)
    const loading = ref(false)
    let graph = null
    
    // 计算属性：是否有数据
    const hasData = computed(() => {
      if (!props.graphData) return false
      
      const data = typeof props.graphData === 'string' 
        ? JSON.parse(props.graphData)
        : props.graphData
        
      return isValidGraphData(data)
    })
    
    // 初始化画布
    const initializeGraph = () => {
      if (!graphContainer.value) return
      
      // 延迟一点初始化，确保DOM已完全渲染
      setTimeout(() => {
        // 创建画布（只读模式）
        graph = initGraph(graphContainer.value, {
          width: graphContainer.value.clientWidth || 800,
          height: graphContainer.value.clientHeight || 500,
          background: {
            color: '#f8f9fa',
          },
          // 预览模式设置
          interactive: props.clickable, // 是否可交互
          interacting: false, // 禁止修改元素
          connecting: false, // 禁止连线
          selecting: false, // 禁止选择
          keyboard: false, // 禁止键盘事件
          // 启用平移和缩放
          panning: {
            enabled: true,
            eventTypes: ['leftMouseDown']
          },
          mousewheel: {
            enabled: true,
            zoomAtMousePosition: true,
            modifiers: 'ctrl',
            minScale: 0.5,
            maxScale: 3,
          },
        })
        
        // 监听节点点击事件
        if (props.clickable) {
          graph.on('node:click', ({ node }) => {
            handleNodeClick(node)
          })
        }
        
        // 如果有数据则加载
        if (props.graphData) {
          loadGraphData()
        }
        
        // 窗口大小变化，自动调整画布大小
        const resizeObserver = new ResizeObserver(() => {
          if (graphContainer.value && graph) {
            graph.resize(
              graphContainer.value.clientWidth,
              graphContainer.value.clientHeight
            )
          }
        })
        
        resizeObserver.observe(graphContainer.value)
        
        return resizeObserver
      }, 100)
    }
    
    // 加载图形数据
    const loadGraphData = () => {
      if (!graph || !props.graphData) return
      
      try {
        loading.value = true
        
        // 将数据转换为图形可用格式
        let data = props.graphData
        
        // 如果是字符串，尝试解析
        if (typeof data === 'string') {
          data = JSON.parse(data)
        }
        
        // 如果是标准化的JSON格式，转换为图形数据
        if (data.nodes && data.edges) {
          data = transformJSONToGraphData(data)
        }
        
        // 清空当前图形
        graph.clearCells()
        
        // 加载数据
        graph.fromJSON(data)
        
        // 适应内容
        graph.zoomToFit({ padding: 20 })
        
        // 高亮节点和设置样式
        setupNodeStyles()
      } catch (error) {
        console.error('加载图形数据失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    // 处理节点点击事件
    const handleNodeClick = (node) => {
      if (!node) return
      
      const nodeData = node.getData() || {}
      const nodeId = node.id
      
      // 查找路由配置
      const routeInfo = props.routeConfig.find(item => 
        (item.id === nodeId) || 
        (nodeData.routeName === item.name && nodeData.routePath === item.path)
      )
      
      // 获取路由路径
      const routePath = routeInfo?.path || nodeData.routePath
      
      if (routePath) {
        // 发送点击事件
        emit('node-click', { 
          id: nodeId, 
          routePath, 
          routeName: routeInfo?.name || nodeData.routeName,
          data: nodeData 
        })
        
        // 如果是有效路由，则导航
        if (routePath.startsWith('/')) {
          router.push(routePath)
        } else if (routePath.startsWith('http')) {
          // 如果是外部链接，打开新窗口
          window.open(routePath, '_blank')
        }
      } else {
        // 没有路由信息，仅发送点击事件
        emit('node-click', { id: nodeId, data: nodeData })
      }
    }
    
    // 设置节点样式
    const setupNodeStyles = () => {
      if (!graph) return
      
      const nodes = graph.getNodes()
      
      nodes.forEach(node => {
        const nodeData = node.getData() || {}
        
        // 如果节点有路由信息，添加可点击样式
        if ((nodeData.routePath || nodeData.routeName) && props.clickable) {
          node.attr({
            body: {
              cursor: 'pointer',
              strokeWidth: 2,
            }
          })
        }
      })
    }
    
    // 生命周期钩子
    onMounted(() => {
      const resizeObserver = initializeGraph()
      
      onBeforeUnmount(() => {
        if (resizeObserver) {
          resizeObserver.disconnect()
        }
        
        if (graph) {
          graph.dispose()
        }
      })
    })
    
    // 监听数据变化
    watch(() => props.graphData, () => {
      if (graph) {
        loadGraphData()
      }
    }, { deep: true })
    
    watch(() => props.routeConfig, () => {
      if (graph) {
        setupNodeStyles()
      }
    }, { deep: true })
    
    return {
      graphContainer,
      loading,
      hasData,
    }
  }
}
</script>

<style scoped>
.mb-x6design-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.mb-x6design-preview-header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f8f9fa;
}

.mb-x6design-preview-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.mb-x6design-preview-content {
  position: relative;
  flex: 1;
  overflow: hidden;
}

.mb-x6design-preview-graph {
  width: 100%;
  height: 100%;
}

.mb-x6design-preview-loading,
.mb-x6design-preview-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
}

.mb-x6design-preview-empty {
  color: #909399;
}
</style> 