<template>
  <div class="mb-logicflow-viewer" :style="{ height: typeof height === 'string' ? height : `${height}px` }">
    <div class="viewer-container" ref="viewerContainer"></div>
  </div>
</template>

<script>
import { ref, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import LogicFlow from '@logicflow/core'
import '@logicflow/core/dist/index.css'

// 导入自定义节点（使用与设计器组件相同的路径）
import { RouteNode } from './nodes/index.js'

export default {
  name: 'MbLogicflowViewer',
  props: {
    // 流程图数据
    data: {
      type: Object,
      default: () => ({
        nodes: [],
        edges: []
      })
    },
    // 容器高度
    height: {
      type: [String, Number],
      default: '400px'
    },
    // 容器宽度
    width: {
      type: [String, Number],
      default: '100%'
    },
    // 是否显示网格
    grid: {
      type: Boolean,
      default: true
    }
  },
  emits: ['node-click', 'ready'],
  setup(props, { emit }) {
    const viewerContainer = ref(null)
    const logicFlow = ref(null)
    const router = useRouter()

    /**
     * 初始化LogicFlow查看器
     */
    const initLogicFlowViewer = async () => {
      if (!viewerContainer.value) {
        console.warn('⚠️ 容器未找到，延迟重试')
        setTimeout(initLogicFlowViewer, 100)
        return
      }

      console.log('🎯 开始初始化LogicFlow查看器')

      try {
        // 确保容器有正确的尺寸，多次检查
        await nextTick()
        
        // 等待容器完全渲染
        let containerWidth = viewerContainer.value.offsetWidth
        let containerHeight = viewerContainer.value.offsetHeight
        
        // 如果容器尺寸为0，重试几次
        let retryCount = 0
        while ((containerWidth === 0 || containerHeight === 0) && retryCount < 5) {
          await new Promise(resolve => setTimeout(resolve, 50))
          containerWidth = viewerContainer.value.offsetWidth
          containerHeight = viewerContainer.value.offsetHeight
          retryCount++
          console.log(`🔄 尺寸重试 ${retryCount}/5:`, { containerWidth, containerHeight })
        }
        
        // 使用默认尺寸作为后备
        const finalWidth = containerWidth || 800
        const finalHeight = containerHeight || 400
        
        console.log('📐 容器尺寸:', {
          offsetWidth: viewerContainer.value.offsetWidth,
          offsetHeight: viewerContainer.value.offsetHeight,
          计算后的宽度: finalWidth,
          计算后的高度: finalHeight,
          props宽度: props.width,
          props高度: props.height,
          重试次数: retryCount
        })

        // 创建LogicFlow实例（只读模式）
        logicFlow.value = new LogicFlow({
          container: viewerContainer.value,
          width: finalWidth,
          height: finalHeight,
          grid: props.grid ? {
            size: 10,
            visible: true,
            type: 'dot',
            config: {
              color: '#DCDCDC',
              thickness: 1
            }
          } : false,
          // 只读模式配置
          edgeType: 'polyline',
          background: {
            color: '#f7f9ff'
          },
          keyboard: {
            enabled: false // 禁用键盘快捷键
          },
          // 禁用所有编辑功能
          isSilentMode: false,
          stopScrollGraph: false,
          stopZoomGraph: false,
          stopMoveGraph: false,
          // 禁用拖拽等编辑操作
          allowRotate: false,
          allowResize: false,
          hoverOutline: false,
          nodeSelectedOutline: false,
          edgeSelectedOutline: false
        })

        // 注册自定义节点
        logicFlow.value.register(RouteNode)

        // 设置为只读模式的规则
        logicFlow.value.setPatternItems([
          'nodeText', // 禁用节点文本编辑
          'edgeText', // 禁用边文本编辑
          'drag', // 禁用拖拽
          'nodeResize', // 禁用节点大小调整
          'selection', // 禁用多选
          'connector', // 禁用连接器
          'nodeMenu', // 禁用节点菜单
          'edgeMenu' // 禁用边菜单
        ])

        // 设置主题，添加更好的悬停效果
        logicFlow.value.setTheme({
          // 节点样式
          baseNode: {
            strokeWidth: 2,
            stroke: '#d9d9d9',
            // 默认无边框高亮
          },
          // 边样式
          baseEdge: {
            strokeWidth: 2,
            stroke: '#1890ff'
          },
          // 文字样式
          nodeText: {
            fontSize: 12,
            fill: '#333333'
          },
          edgeText: {
            fontSize: 11,
            fill: '#666666'
          }
        })

        // 添加鼠标悬停事件监听
        logicFlow.value.on('node:mouseenter', ({ data }) => {
          console.log('🖱️ 鼠标进入节点:', data.id)
          // 设置悬停时的边框样式
          logicFlow.value.setProperties(data.id, {
            ...data.properties,
            hovered: true
          })
          // 强制刷新节点样式
          nextTick(() => {
            const nodeModel = logicFlow.value.getNodeModelById(data.id)
            if (nodeModel) {
              nodeModel.setAttributes(nodeModel.getNodeStyle())
            }
          })
        })

        logicFlow.value.on('node:mouseleave', ({ data }) => {
          console.log('🖱️ 鼠标离开节点:', data.id)
          // 恢复默认边框样式
          logicFlow.value.setProperties(data.id, {
            ...data.properties,
            hovered: false
          })
          // 强制刷新节点样式
          nextTick(() => {
            const nodeModel = logicFlow.value.getNodeModelById(data.id)
            if (nodeModel) {
              nodeModel.setAttributes(nodeModel.getNodeStyle())
            }
          })
        })

        // 添加节点点击事件监听
        logicFlow.value.on('node:click', ({ data }) => {
          console.log('🎯 节点被点击:', data)
          
          // 发射节点点击事件
          emit('node-click', data)
          
          // 如果节点有路由信息，则跳转
          if (data.properties && data.properties.routePath) {
            console.log('🚀 准备跳转路由:', data.properties.routePath)
            
            try {
              router.push(data.properties.routePath)
              console.log('✅ 路由跳转成功')
            } catch (error) {
              console.warn('⚠️ 路由跳转失败:', error)
              // 如果Vue Router跳转失败，尝试使用原生跳转
              window.location.href = data.properties.routePath
            }
          } else {
            console.warn('⚠️ 节点没有配置路由信息')
          }
        })

        // 渲染数据
        if (props.data && (props.data.nodes.length > 0 || props.data.edges.length > 0)) {
          logicFlow.value.render(props.data)
          console.log('📊 流程图数据渲染完成:', props.data)
          
          // 不自动适应画布，保持100%原始尺寸
          // nextTick(() => {
          //   logicFlow.value.fitView()
          // })
        }

        console.log('✅ LogicFlow查看器初始化完成')
        emit('ready', logicFlow.value)

      } catch (error) {
        console.error('❌ LogicFlow查看器初始化失败:', error)
      }
    }

    /**
     * 更新流程图数据
     */
    const updateData = (newData) => {
      if (logicFlow.value && newData) {
        console.log('🔄 更新流程图数据:', newData)
        logicFlow.value.render(newData)
        
        // 不自动适应画布，保持100%原始尺寸
        // nextTick(() => {
        //   logicFlow.value.fitView()
        // })
      }
    }

    /**
     * 适应画布大小
     */
    const fitView = () => {
      if (logicFlow.value) {
        logicFlow.value.fitView()
      }
    }

    /**
     * 获取当前数据
     */
    const getData = () => {
      if (logicFlow.value) {
        return logicFlow.value.getGraphData()
      }
      return null
    }

    // 监听数据变化
    watch(() => props.data, (newData) => {
      if (newData) {
        updateData(newData)
      }
    }, { deep: true })

    // 组件挂载后初始化
    onMounted(() => {
      // 使用setTimeout确保DOM完全渲染
      setTimeout(() => {
        initLogicFlowViewer()
      }, 100)
    })

    return {
      viewerContainer,
      logicFlow,
      updateData,
      fitView,
      getData
    }
  }
}
</script>

<style scoped>
.mb-logicflow-viewer {
  width: 100%;
  height: 100%;
  position: relative;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  background: #f7f9ff;
}

.viewer-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 设置节点为可点击状态 */
:deep(.lf-node) {
  cursor: pointer !important;
  transition: all 0.2s ease;
}

/* 悬停效果 - 仅添加轻微的阴影，不改变大小 */
:deep(.lf-node:hover) {
  cursor: pointer !important;
  /* 移除可能导致"动了一下"的效果 */
  /* filter: brightness(1.1); */
  /* transform: scale(1.02); */
  
  /* 添加轻微阴影增强交互感 */
  filter: drop-shadow(0 4px 8px rgba(24, 144, 255, 0.2));
}

/* 确保所有节点元素都显示小手光标 */
:deep(.lf-node rect),
:deep(.lf-node circle),
:deep(.lf-node path),
:deep(.lf-node polygon),
:deep(.lf-node text) {
  cursor: pointer !important;
}

/* 隐藏编辑相关的UI元素 */
:deep(.lf-anchor) {
  display: none !important;
}

:deep(.lf-outline) {
  display: none !important;
}

:deep(.lf-resize-control) {
  display: none !important;
}

:deep(.lf-menu) {
  display: none !important;
}
</style> 