<script setup>
import { computed } from 'vue';
import AdminLayout from '@/components/admin-layout';
import { LAYOUT_SCROLL_EL_ID } from '@/components/admin-layout/shared';
import { useThemeStore } from '@/store/modules/themeStore';
import { useAppStore } from '@/store/modules/appStore';
import { setupMixMenuContext } from './context';
import GlobalHeader from './modules/global-header.vue';
import GlobalSider from './modules/global-sider.vue';
import GlobalTab from './modules/global-tab.vue';
import GlobalContent from './modules/global-content.vue';
import GlobalFooter from './modules/global-footer.vue';

defineOptions({
  name: 'BaseLayout'
});

const themeStore = useThemeStore();
const appStore = useAppStore();
const { childLevelMenus, isActiveFirstLevelMenuHasChildren } = setupMixMenuContext();

const layoutMode = computed(() => {
  const vertical = 'vertical';
  const horizontal = 'horizontal';
  return themeStore.layout.mode.includes(vertical) ? vertical : horizontal;
});

const isVerticalMix = computed(() => themeStore.layout.mode === 'vertical-mix');

const isHorizontalMix = computed(() => themeStore.layout.mode === 'horizontal-mix');

const siderVisible = computed(() => themeStore.layout.mode !== 'horizontal');

const siderWidth = computed(() => {
  const { sider } = themeStore;
  const { mode } = themeStore.layout;

  if (mode === 'vertical-mix') {
    return sider.mixWidth;
  }

  if (mode === 'horizontal-mix') {
    return sider.width;
  }

  return sider.width;
});

const siderCollapsedWidth = computed(() => {
  const { sider } = themeStore;
  const { mode } = themeStore.layout;

  if (mode === 'vertical-mix') {
    return sider.mixCollapsedWidth;
  }

  if (mode === 'horizontal-mix') {
    return sider.collapsedWidth;
  }

  return sider.collapsedWidth;
});
</script>

<template>
  <AdminLayout v-model:sider-collapse="appStore.siderCollapse" :mode="layoutMode" :scroll-el-id="LAYOUT_SCROLL_EL_ID"
    :scroll-mode="themeStore.layout.scrollMode" :is-mobile="appStore.isMobile" :full-content="appStore.fullContent"
    :fixed-top="themeStore.fixedHeaderAndTab" :header-visible="true" :header-height="themeStore.header.height"
    :tab-visible="themeStore.tab.visible" :tab-height="themeStore.tab.height"
    :content-class="appStore.contentXScrollable ? 'overflow-x-hidden' : ''" :sider-visible="siderVisible"
    :sider-width="siderWidth" :sider-collapsed-width="siderCollapsedWidth" :footer-visible="themeStore.footer.visible"
    :footer-height="themeStore.footer.height" :fixed-footer="themeStore.footer.fixed"
    :right-footer="themeStore.footer.right">
    <template #header>
      <GlobalHeader />
    </template>
    <template #tab>
      <GlobalTab />
    </template>
    <template #sider>
      <GlobalSider />
    </template>
    <GlobalContent />
    <template #footer>
      <GlobalFooter />
    </template>
  </AdminLayout>
</template>

<style scoped>
/* 滚动条样式 */
:deep(#__SCROLL_EL_ID__) {
  scrollbar-width: thin;
  scrollbar-color: #e6e6e6 #f7f7f7;
}

:deep(#__SCROLL_EL_ID__::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(#__SCROLL_EL_ID__::-webkit-scrollbar-thumb) {
  background-color: #e6e6e6;
  border-radius: 3px;
  border: 1px solid #e0e0e0;
}

:deep(#__SCROLL_EL_ID__::-webkit-scrollbar-track) {
  background-color: #f7f7f7;
  border: 1px solid #efefef;
}
</style>
