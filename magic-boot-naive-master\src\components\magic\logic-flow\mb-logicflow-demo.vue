<template>
  <div class="mb-logicflow-demo">
    <!-- LogicFlow 路由设计器 -->
    <n-card title="LogicFlow 路由设计器" class="designer-card">
      <div class="demo-container">
        <mb-logicflow-design 
          ref="logicFlowRef"
          :initial-data="initialData"
          :show-property-panel="true"
          @save="handleSave"
          @clear="handleClear"
          @node-select="handleNodeSelect"
          @edge-select="handleEdgeSelect"
        />
      </div>
    </n-card>
    
    <!-- 流程图查看器测试区域 -->
    <n-card title="流程图查看器 (点击节点可跳转路由)" class="viewer-card">
      <template #header-extra>
        <n-tag size="small" type="info">
          节点数: {{ viewerData.nodes?.length || 0 }} | 连线数: {{ viewerData.edges?.length || 0 }}
        </n-tag>
      </template>
      <div class="viewer-container">
        <mb-logicflow-viewer 
          :data="viewerData"
          height="400px"
          :grid="true"
          @node-click="handleNodeClick"
          @ready="handleViewerReady"
        />
      </div>
    </n-card>
    
    <!-- 保存的数据展示 -->
    <n-card title="保存的数据" class="data-card" v-if="savedData">
      <div class="data-actions">
        <n-button @click="applyToViewer" type="primary" size="small">
          应用到查看器
        </n-button>
        <n-button @click="resetViewer" size="small">
          重置查看器
        </n-button>
      </div>
      <n-code :code="JSON.stringify(savedData, null, 2)" language="json" />
    </n-card>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { NCard, NCode, NButton, NTag } from 'naive-ui'

export default {
  name: 'MbLogicflowDemo',
  components: {
    NCard,
    NCode,
    NButton,
    NTag
  },
  props: {
    // 支持外部传入初始数据
    defaultData: {
      type: Object,
      default: () => ({
        nodes: [],
        edges: []
      })
    },
    // 是否自动同步保存数据到查看器
    autoSync: {
      type: Boolean,
      default: true
    },
    // 设计器高度
    designerHeight: {
      type: String,
      default: '700px'
    },
    // 查看器高度
    viewerHeight: {
      type: String,
      default: '400px'
    }
  },
  emits: ['save', 'node-click', 'designer-ready', 'viewer-ready'],
  setup(props, { emit }) {
    console.log('🎯 LogicFlow Demo组件开始初始化')
    
    // LogicFlow设计器引用
    const logicFlowRef = ref(null)
    
    // 初始数据
    const initialData = ref(props.defaultData)
    
    // 保存的数据
    const savedData = ref(null)
    
    // 查看器数据（可以独立于保存数据）
    const viewerData = ref({
      "nodes": [
        {
          "id": "290211aa-f0bd-4a05-b3ca-0a02571e946c",
          "type": "route-node",
          "x": 474,
          "y": 237.203125,
          "properties": {
            "functionName": "借货",
            "routePath": "/system/mobile/menu-list-mobile",
            "iconSvg": "/src/x6-design-icons/route/借货.svg",
            "description": "借货功能模块的详细描述",
            "width": 120,
            "height": 80
          },
          "text": {
            "x": 474,
            "y": 255.203125,
            "value": "借货"
          }
        },
        {
          "id": "2b37c38a-e88e-4c8b-ad60-8fac5272fc5a",
          "type": "route-node",
          "x": 873,
          "y": 217.203125,
          "properties": {
            "functionName": "仓库库存",
            "routePath": "/warehouse-inventory",
            "iconSvg": "/src/x6-design-icons/route/仓库库存.svg",
            "description": "仓库库存功能模块的详细描述",
            "width": 120,
            "height": 80
          },
          "text": {
            "x": 873,
            "y": 235.203125,
            "value": "仓库库存"
          }
        }
      ],
      "edges": [
        {
          "id": "5f0e5e24-ba25-4610-a0a1-2edb60cc7680",
          "type": "polyline",
          "properties": {},
          "sourceNodeId": "290211aa-f0bd-4a05-b3ca-0a02571e946c",
          "targetNodeId": "2b37c38a-e88e-4c8b-ad60-8fac5272fc5a",
          "sourceAnchorId": "290211aa-f0bd-4a05-b3ca-0a02571e946c_1",
          "targetAnchorId": "2b37c38a-e88e-4c8b-ad60-8fac5272fc5a_3",
          "startPoint": {
            "x": 534,
            "y": 237.203125
          },
          "endPoint": {
            "x": 813,
            "y": 217.203125
          },
          "pointsList": [
            {
              "x": 534,
              "y": 237.203125
            },
            {
              "x": 673.5,
              "y": 237.203125
            },
            {
              "x": 673.5,
              "y": 217.203125
            },
            {
              "x": 813,
              "y": 217.203125
            }
          ]
        }
      ]
    })
    
         /**
      * 处理保存事件 - 核心功能：保存后自动更新查看器
      */
     const handleSave = (graphData) => {
       savedData.value = graphData
       console.log('💾 LogicFlow保存数据:', graphData)
       
       // 如果开启自动同步，立即更新查看器
       if (props.autoSync && savedData.value) {
         viewerData.value = { ...savedData.value }
         console.log('🔄 自动同步保存的数据到查看器:', savedData.value)
       }
       
       // 向父组件发送保存事件
       emit('save', graphData)
     }
    
    /**
     * 处理清空事件
     */
    const handleClear = () => {
      savedData.value = null
      console.log('🗑️ LogicFlow清空数据')
      
      // 如果开启自动同步，清空查看器
      if (props.autoSync) {
        viewerData.value = { nodes: [], edges: [] }
      }
    }
    
    /**
     * 处理节点选中事件
     */
    const handleNodeSelect = (nodeData) => {
      console.log('🎯 选中节点:', nodeData)
    }
    
    /**
     * 处理连线选中事件
     */
    const handleEdgeSelect = (edgeData) => {
      console.log('🎯 选中连线:', edgeData)
    }
    
    /**
     * 处理查看器节点点击事件
     */
    const handleNodeClick = (nodeData) => {
      console.log('🔥 查看器节点被点击:', nodeData)
      
      // 向父组件发送节点点击事件
      emit('node-click', nodeData)
      
      // 如果有路由地址，可以进行路由跳转
      if (nodeData.properties?.routePath) {
        console.log('🚀 准备跳转到路由:', nodeData.properties.routePath)
        // 这里可以添加路由跳转逻辑
        // this.$router.push(nodeData.properties.routePath)
      }
    }
    
    /**
     * 处理查看器准备就绪事件
     */
    const handleViewerReady = (viewerInstance) => {
      console.log('✅ 流程图查看器准备就绪:', viewerInstance)
      emit('viewer-ready', viewerInstance)
    }
    
         /**
      * 手动应用保存的数据到查看器
      */
     const applyToViewer = () => {
       if (savedData.value) {
         viewerData.value = { ...savedData.value }
         console.log('🔄 应用保存的数据到查看器:', savedData.value)
       }
     }
    
    /**
     * 重置查看器到默认数据
     */
    const resetViewer = () => {
      // 重置到初始示例数据
      viewerData.value = {
        "nodes": [
          {
            "id": "290211aa-f0bd-4a05-b3ca-0a02571e946c",
            "type": "route-node",
            "x": 474,
            "y": 237.203125,
            "properties": {
              "functionName": "借货",
              "routePath": "/system/mobile/menu-list-mobile",
              "iconSvg": "/src/x6-design-icons/route/借货.svg",
              "description": "借货功能模块的详细描述",
              "width": 120,
              "height": 80
            },
            "text": {
              "x": 474,
              "y": 255.203125,
              "value": "借货"
            }
          },
          {
            "id": "2b37c38a-e88e-4c8b-ad60-8fac5272fc5a",
            "type": "route-node",
            "x": 873,
            "y": 217.203125,
            "properties": {
              "functionName": "仓库库存",
              "routePath": "/warehouse-inventory",
              "iconSvg": "/src/x6-design-icons/route/仓库库存.svg",
              "description": "仓库库存功能模块的详细描述",
              "width": 120,
              "height": 80
            },
            "text": {
              "x": 873,
              "y": 235.203125,
              "value": "仓库库存"
            }
          }
        ],
        "edges": [
          {
            "id": "5f0e5e24-ba25-4610-a0a1-2edb60cc7680",
            "type": "polyline",
            "properties": {},
            "sourceNodeId": "290211aa-f0bd-4a05-b3ca-0a02571e946c",
            "targetNodeId": "2b37c38a-e88e-4c8b-ad60-8fac5272fc5a",
            "sourceAnchorId": "290211aa-f0bd-4a05-b3ca-0a02571e946c_1",
            "targetAnchorId": "2b37c38a-e88e-4c8b-ad60-8fac5272fc5a_3",
            "startPoint": {
              "x": 534,
              "y": 237.203125
            },
            "endPoint": {
              "x": 813,
              "y": 217.203125
            },
            "pointsList": [
              {
                "x": 534,
                "y": 237.203125
              },
              {
                "x": 673.5,
                "y": 237.203125
              },
              {
                "x": 673.5,
                "y": 217.203125
              },
              {
                "x": 813,
                "y": 217.203125
              }
            ]
          }
        ]
      }
      console.log('🔄 重置查看器数据到默认示例')
    }
    
    /**
     * 获取设计器实例
     */
    const getDesignerInstance = () => {
      return logicFlowRef.value
    }
    
    /**
     * 获取当前保存的数据
     */
    const getSavedData = () => {
      return savedData.value
    }
    
    /**
     * 获取查看器当前数据
     */
    const getViewerData = () => {
      return viewerData.value
    }
    
    return {
      logicFlowRef,
      initialData,
      savedData,
      viewerData,
      handleSave,
      handleClear,
      handleNodeSelect,
      handleEdgeSelect,
      handleNodeClick,
      handleViewerReady,
      applyToViewer,
      resetViewer,
      getDesignerInstance,
      getSavedData,
      getViewerData
    }
  }
}
</script>

<style scoped>
.mb-logicflow-demo {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.designer-card {
  flex: 0 0 auto;
}

.demo-container {
  height: v-bind(designerHeight);
  width: 100%;
  position: relative;
}

.viewer-card {
  flex: 0 0 auto;
  min-height: 450px;
}

.viewer-container {
  height: v-bind(viewerHeight);
  width: 100%;
  position: relative;
}

.data-card {
  flex: 0 0 auto;
  min-height: 350px;
  max-height: 500px;
}

.data-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

/* 确保JSON代码区域可以滚动 */
:deep(.n-code) {
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .mb-logicflow-demo {
    gap: 16px;
    padding: 16px;
  }
  
  .demo-container {
    height: 600px;
  }
  
  .viewer-container {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .mb-logicflow-demo {
    gap: 12px;
    padding: 12px;
  }
  
  .demo-container {
    height: 500px;
  }
  
  .viewer-container {
    height: 300px;
  }
  
  .data-actions {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 