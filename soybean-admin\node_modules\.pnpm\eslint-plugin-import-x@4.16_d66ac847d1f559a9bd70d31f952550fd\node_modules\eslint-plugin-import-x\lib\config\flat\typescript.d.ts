declare const _default: {
    settings: {
        'import-x/extensions': readonly [".ts", ".tsx", ".cts", ".mts", ".js", ".jsx", ".cjs", ".mjs"];
        'import-x/external-module-folders': string[];
        'import-x/parsers': {
            '@typescript-eslint/parser': (".ts" | ".tsx" | ".cts" | ".mts")[];
        };
        'import-x/resolver': {
            typescript: true;
        };
    };
    rules: {
        'import-x/named': "off";
    };
};
export default _default;
