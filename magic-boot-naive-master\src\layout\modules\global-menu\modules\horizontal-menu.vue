<script setup>
import { computed, h } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/userStore';
import { useMenu } from '../../../context';
import MbIcon from '@/components/magic/basic/mb-icon.vue';

defineOptions({
  name: 'HorizontalMenu'
});

const router = useRouter();
const userStore = useUserStore();
const { selectedKey } = useMenu();

// 处理菜单数据
const menuOptions = computed(() => {
  const menus = userStore.getPermissionRouters || [];
  return processMenus(menus);
});

function processMenus(menus) {
  return menus.map(menu => ({
    label: menu.title,
    key: menu.path,
    icon: menu.icon ? () => h(MbIcon, { icon: menu.icon, size: '18px' }) : undefined,
    children: menu.children ? processMenus(menu.children) : undefined
  }));
}

function handleMenuSelect(key) {
  router.push(key);
}
</script>

<template>
  <div id="global-header-menu" class="flex-1">
    <n-menu mode="horizontal" :value="selectedKey" :options="menuOptions" :indent="18" responsive
      @update:value="handleMenuSelect" />
  </div>
</template>

<style scoped></style>
