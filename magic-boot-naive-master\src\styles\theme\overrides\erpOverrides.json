{"Pagination": {"buttonIconSizeMedium": "14px", "itemSizeMedium": "22px", "itemFontSizeMedium": "12px", "jumperFontSizeMedium": "12px"}, "Switch": {"railHeightMedium": "18px", "buttonWidthMedium": "14px", "buttonHeightMedium": "14px", "railWidthMedium": "32px"}, "Form": {"feedbackFontSizeMedium": "12px", "labelFontSizeLeftMedium": "12px", "labelFontSizeTopMedium": "12px", "labelHeightMedium": "24px", "blankHeightMedium": "24px"}, "Input": {"fontSizeMedium": "12px", "heightMedium": "24px"}, "Button": {"fontSizeMedium": "12px", "paddingMedium": "0 12px", "heightMedium": "24px"}, "DataTable": {"thColor": "rgba(230, 230, 230, 1)", "thPaddingMedium": "3px 0px", "tdPaddingMedium": "3px 0px", "tdColorHover": "rgba(188, 213, 230, 1)", "actionDividerColor": "rgba(239, 239, 245, 1)", "tdColorStriped": "rgba(249, 249, 251, 1)", "fontSizeMedium": "12px"}, "common": {"primaryColor": "#2D8CF0FF", "primaryColorHover": "#6FAFF4FF", "primaryColorPressed": "#2D8CF0FF", "primaryColorSuppl": "#62AEFFFF", "heightMedium": "24px", "fontSizeMedium": "12px"}, "LoadingBar": {"colorLoading": "#2d8cf0FF"}, "Menu": {"itemTextColorActive": "#2d8cf0FF", "itemTextColorActiveHover": "#2d8cf0FF", "itemTextColorChildActive": "#2D8CF0FF", "itemTextColorChildActiveHover": "#2D8CF0FF", "itemTextColorActiveHorizontal": "#2D8CF0FF", "itemTextColorActiveHoverHorizontal": "#2D8CF0FF", "itemTextColorChildActiveHorizontal": "#2D8CF0FF", "itemTextColorChildActiveHoverHorizontal": "#2D8CF0FF", "itemIconColorActive": "#2D8CF0FF", "itemIconColorActiveHover": "#2D8CF0FF", "itemIconColorChildActive": "#2D8CF0FF", "itemIconColorChildActiveHover": "#2D8CF0FF", "itemIconColorActiveHorizontal": "#2D8CF0FF", "itemIconColorActiveHoverHorizontal": "#2D8CF0FF", "itemIconColorChildActiveHorizontal": "#2D8CF0FF", "itemIconColorChildActiveHoverHorizontal": "#2D8CF0FF", "arrowColorActive": "#2D8CF0FF", "arrowColorActiveHover": "#2D8CF0FF", "arrowColorChildActive": "#2D8CF0FF", "arrowColorChildActiveHover": "#2D8CF0FF", "itemColorActiveInverted": "#2D8CF0FF", "itemColorActiveHoverInverted": "#2D8CF0FF", "itemColorActiveCollapsedInverted": "#2D8CF0FF", "itemColorActive": "rgba(45, 140, 240, 0.1)", "itemColorActiveHover": "#2D8CF01A", "itemColorActiveCollapsed": "#2D8CF01A", "itemTextColorHoverHorizontal": "#2D8CF0FF", "itemIconColorHoverHorizontal": "#2D8CF0FF"}}