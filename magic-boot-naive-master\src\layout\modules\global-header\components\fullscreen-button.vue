<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

defineOptions({
  name: 'FullscreenButton'
});

const isFullscreen = ref(false);

// 切换全屏
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().then(() => {
      isFullscreen.value = true;
    }).catch(err => {
      console.error('进入全屏失败:', err);
    });
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen().then(() => {
        isFullscreen.value = false;
      }).catch(err => {
        console.error('退出全屏失败:', err);
      });
    }
  }
}

// 监听全屏状态变化
function handleFullscreenChange() {
  isFullscreen.value = !!document.fullscreenElement;
}

onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  // 初始化状态
  isFullscreen.value = !!document.fullscreenElement;
});

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
});
</script>

<template>
  <n-tooltip placement="bottom">
    <template #trigger>
      <n-button
        quaternary
        circle
        @click="toggleFullscreen"
        class="fullscreen-button"
      >
        <template #icon>
          <n-icon size="18">
            <!-- 全屏图标 -->
            <svg v-if="!isFullscreen" viewBox="0 0 24 24">
              <path 
                fill="currentColor" 
                d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" 
              />
            </svg>
            <!-- 退出全屏图标 -->
            <svg v-else viewBox="0 0 24 24">
              <path 
                fill="currentColor" 
                d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z" 
              />
            </svg>
          </n-icon>
        </template>
      </n-button>
    </template>
    {{ isFullscreen ? '退出全屏' : '进入全屏' }}
  </n-tooltip>
</template>

<style scoped>
.fullscreen-button {
  transition: all 0.2s ease;
}

.fullscreen-button:hover {
  background-color: var(--n-item-color-hover);
}
</style>
