{"name": "soybean-admin", "type": "module", "version": "1.3.15", "description": "A fresh and elegant admin template, based on Vue3、Vite7、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite7、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite7", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "engines": {"node": ">=20.19.0", "pnpm": ">=10.5.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/vue": "5.0.0", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@vueuse/core": "13.5.0", "clipboard": "2.0.11", "dayjs": "1.11.13", "defu": "6.1.4", "echarts": "5.6.0", "json5": "2.2.3", "naive-ui": "2.42.0", "nprogress": "0.2.0", "pinia": "3.0.3", "tailwind-merge": "3.3.1", "vue": "3.5.17", "vue-draggable-plus": "0.6.0", "vue-i18n": "11.1.9", "vue-router": "4.5.1"}, "devDependencies": {"@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.357", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.7.1", "@types/node": "24.0.13", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "66.3.3", "@unocss/preset-icons": "66.3.3", "@unocss/preset-uno": "66.3.3", "@unocss/transformer-directives": "66.3.3", "@unocss/transformer-variant-group": "66.3.3", "@unocss/vite": "66.3.3", "@vitejs/plugin-vue": "6.0.0", "@vitejs/plugin-vue-jsx": "5.0.1", "consola": "3.4.2", "eslint": "9.31.0", "eslint-plugin-vue": "10.3.0", "kolorist": "1.8.0", "sass": "1.89.2", "simple-git-hooks": "2.13.0", "tsx": "4.20.3", "typescript": "5.8.3", "unplugin-icons": "22.1.0", "unplugin-vue-components": "28.8.0", "vite": "7.0.4", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.7.7", "vue-eslint-parser": "10.2.0", "vue-tsc": "3.0.1"}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint && git diff --exit-code"}, "website": "https://admin.soybeanjs.cn"}