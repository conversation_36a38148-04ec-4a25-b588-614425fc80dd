<script setup>
import { ref, computed, nextTick, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/userStore';

defineOptions({
  name: 'MenuSearch'
});

const router = useRouter();
const userStore = useUserStore();

const visible = ref(false);
const searchValue = ref('');
const searchInputRef = ref(null);

// 获取所有菜单项（扁平化）
const allMenus = computed(() => {
  const menus = userStore.getPermissionRouters || [];
  return flattenMenus(menus);
});

// 搜索结果
const searchResults = computed(() => {
  if (!searchValue.value.trim()) {
    return allMenus.value.slice(0, 10); // 显示前10个菜单项
  }
  
  const keyword = searchValue.value.toLowerCase();
  return allMenus.value.filter(menu => 
    menu.title.toLowerCase().includes(keyword) ||
    menu.path.toLowerCase().includes(keyword)
  ).slice(0, 10);
});

// 扁平化菜单
function flattenMenus(menus, parentPath = '') {
  const result = [];
  
  for (const menu of menus) {
    const fullPath = parentPath ? `${parentPath} > ${menu.title}` : menu.title;
    
    result.push({
      title: menu.title,
      path: menu.path,
      fullPath,
      icon: menu.icon
    });
    
    if (menu.children && menu.children.length > 0) {
      result.push(...flattenMenus(menu.children, fullPath));
    }
  }
  
  return result;
}

// 打开搜索
function openSearch() {
  visible.value = true;
  nextTick(() => {
    searchInputRef.value?.focus();
  });
}

// 关闭搜索
function closeSearch() {
  visible.value = false;
  searchValue.value = '';
}

// 选择菜单项
function selectMenu(menu) {
  router.push(menu.path);
  closeSearch();
}

// 键盘事件处理
function handleKeydown(event) {
  if (event.key === 'Escape') {
    closeSearch();
  }
}

// 监听搜索框显示状态
watch(visible, (newVisible) => {
  if (newVisible) {
    document.addEventListener('keydown', handleKeydown);
  } else {
    document.removeEventListener('keydown', handleKeydown);
  }
});

// 暴露打开搜索的方法
defineExpose({
  openSearch
});
</script>

<template>
  <!-- 搜索触发按钮 -->
  <n-button quaternary circle @click="openSearch">
    <template #icon>
      <n-icon size="18">
        <svg viewBox="0 0 24 24">
          <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
      </n-icon>
    </template>
  </n-button>

  <!-- 搜索弹窗 -->
  <n-modal
    v-model:show="visible"
    preset="card"
    title="菜单搜索"
    size="medium"
    :bordered="false"
    :segmented="false"
    :closable="true"
    style="max-width: 600px;"
    @close="closeSearch"
  >
    <div class="menu-search">
      <!-- 搜索输入框 -->
      <n-input
        ref="searchInputRef"
        v-model:value="searchValue"
        placeholder="搜索菜单..."
        clearable
        size="large"
        class="mb-4"
      >
        <template #prefix>
          <n-icon size="18">
            <svg viewBox="0 0 24 24">
              <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>
          </n-icon>
        </template>
      </n-input>

      <!-- 搜索结果 -->
      <div class="search-results">
        <div
          v-for="menu in searchResults"
          :key="menu.path"
          class="search-result-item"
          @click="selectMenu(menu)"
        >
          <div class="flex items-center">
            <n-icon v-if="menu.icon" size="16" class="mr-3">
              <component :is="menu.icon" />
            </n-icon>
            <div class="flex-1">
              <div class="menu-title">{{ menu.title }}</div>
              <div class="menu-path">{{ menu.fullPath }}</div>
            </div>
          </div>
        </div>
        
        <div v-if="searchResults.length === 0" class="no-results">
          <n-empty description="未找到相关菜单" />
        </div>
      </div>
    </div>
  </n-modal>
</template>

<style scoped>
.menu-search {
  max-height: 400px;
}

.search-results {
  max-height: 300px;
  overflow-y: auto;
}

.search-result-item {
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-result-item:hover {
  background-color: var(--n-item-color-hover);
}

.menu-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--n-text-color);
}

.menu-path {
  font-size: 12px;
  color: var(--n-text-color-disabled);
  margin-top: 2px;
}

.no-results {
  padding: 20px;
  text-align: center;
}
</style>
