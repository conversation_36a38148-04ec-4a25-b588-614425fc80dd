<script setup>
import { computed } from 'vue';

defineOptions({
  name: 'GlobalFooter'
});

const currentYear = computed(() => new Date().getFullYear());
</script>

<template>
  <div class="global-footer h-full flex items-center justify-center bg-container border-t border-gray-200">
    <div class="footer-content text-center">
      <p class="text-sm text-gray-500">
        © {{ currentYear }} MagicBoot. All rights reserved.
      </p>
    </div>
  </div>
</template>

<style scoped>
.bg-container {
  background-color: var(--container-color, rgb(255, 255, 255));
}

.h-full {
  height: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center;
}

.text-sm {
  font-size: 0.875rem;
}

.text-gray-500 {
  color: rgb(107, 114, 128);
}

.border-t {
  border-top-width: 1px;
}

.border-gray-200 {
  border-color: rgb(229, 231, 235);
}
</style>
