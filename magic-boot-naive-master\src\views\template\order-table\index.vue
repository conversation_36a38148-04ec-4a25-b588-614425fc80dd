<template>
  <div class="main-container">
    <!-- 搜索区域 -->
    <mb-search style="margin-top: 8px;margin-bottom: -8px;" :where="tableOptions.where" @search="reloadTable"
      class="search-row" />

    <!-- 状态筛选标签页 -->
    <n-flex style="margin-bottom: 2px;">
      <template v-for="(filterCondition, index) in filterConditionSchema" :key="filterCondition.name">
        <n-tabs type="line" v-model:value="filterCondition.activeKey"
          @update:value="value => handleFilterTabChange(filterCondition.name, value)">
          <n-tab v-for="item in filterCondition.items" :key="item.field" :name="item.field">
            <n-badge :color="item.color" dot style="margin-right: 5px;" />
            {{ item.label + '(' + item.count + ')' }}
          </n-tab>
        </n-tabs>
        <n-divider v-if="index + 1 !== filterConditionSchema.length" vertical />
      </template>
    </n-flex>

    <!-- 主档表格批量操作工具栏 -->
    <div class="batch-toolbar">
      <n-space justify="space-between">
        <n-text type="info">
          {{ masterCheckedRowKeys.length > 0 ? `已选择 ${masterCheckedRowKeys.length} 条记录` : '未选择任何记录' }}
        </n-text>
        <n-space>
          <n-button :size="$global.uiSize.value" type="error" @click="handleMasterBatchDelete"
            :disabled="masterCheckedRowKeys.length === 0">
            <template #icon>
              <mb-icon icon="TrashOutline" />
            </template>
            批量删除
          </n-button>
          <n-button :size="$global.uiSize.value" type="warning" @click="handleMasterBatchEdit"
            :disabled="masterCheckedRowKeys.length === 0">
            <template #icon>
              <mb-icon icon="CreateOutline" />
            </template>
            批量编辑
          </n-button>
          <n-button :size="$global.uiSize.value" type="success" @click="handleMasterBatchExport"
            :disabled="masterCheckedRowKeys.length === 0">
            <template #icon>
              <mb-icon icon="DownloadOutline" />
            </template>
            批量导出
          </n-button>
          <n-button :size="$global.uiSize.value" @click="masterCheckedRowKeys = []; masterSelectedRows = []"
            :disabled="masterCheckedRowKeys.length === 0">
            取消选择
          </n-button>
        </n-space>
      </n-space>
    </div>

    <!-- 主表格 -->
    <mb-table ref="table" v-bind="tableOptions" v-model:checked-row-keys="masterCheckedRowKeys"
      @update:checked-row-datas="handleMasterCheckedRowKeysChange" @selectedRow="orderRowChanged"
      :loading="tableLoading" :style="{
        flex: 1,
        height: tableHeight + 'px',
        maxHeight: tableHeight + 'px'
      }" />

    <!-- 订单详情抽屉 -->
    <n-drawer v-model:show="showDrawer" width="96%" :height="800" placement="right" :on-after-enter="onDrawerOpen">
      <n-drawer-content title="送货单查看" closable>

        <!-- 原先订单详情Tab中的内容移动到这里 -->
        <n-form class="order-form" label-placement="left" label-width="80" :model="bill.master">
          <n-form-item class='form-item-wrapper' label="送货单号">
            <n-input class='item-input' v-model:value="bill.master.billnum" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="单据日期">
            <n-input class='item-input' v-model:value="bill.master.billdate" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="单据类型">
            <n-input class='item-input' v-model:value="bill.master.pbillType" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="供应商">
            <n-input class='item-input' v-model:value="bill.master.suppName" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="采购日期">
            <n-input class='item-input' v-model:value="bill.master.pbillDate" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="状态">
            <n-input class='item-input' v-model:value="bill.master.mesStatus" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="是否使用">
            <n-input class='item-input' v-model:value="bill.master.actived" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="备注">
            <n-input class='item-input' v-model:value="bill.master.memo" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="创建人">
            <n-input class='item-input' v-model:value="bill.master.maker" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="创建日期">
            <n-input class='item-input' v-model:value="bill.master.makedate" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="修改人">
            <n-input class='item-input' v-model:value="bill.master.modifyby" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="修改日期">
            <n-input class='item-input' v-model:value="bill.master.modifydate" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="工厂">
            <n-input class='item-input' v-model:value="bill.master.factroy" readonly />
          </n-form-item>

          <n-form-item class='form-item-wrapper' label="公司">
            <n-input class='item-input' v-model:value="bill.master.company" readonly />
          </n-form-item>
        </n-form>
        <n-divider />

        <n-form class="order-form" label-placement="left" label-width="100" :model="curDetailRow">
          <!-- 采购订单号 -->
          <n-form-item class="form-item-wrapper" label="采购订单号">
            <mb-select-table v-bind="editorTableFormOptions" v-model="curDetailRow.purorderno"
              :search="searchOptions" />
          </n-form-item>

          <!-- 采购订单行号 -->
          <n-form-item class="form-item-wrapper" label="采购订单行号">
            <n-input class="item-input" v-model:value="curDetailRow.purorderseq" />
          </n-form-item>

          <!-- 物料编码 -->
          <n-form-item class="form-item-wrapper" label="物料编号">
            <n-input class="item-input" v-model:value="curDetailRow.itemno" />
          </n-form-item>

          <!-- 物料名称 -->
          <n-form-item class="form-item-wrapper" label="物料名称">
            <n-input class="item-input" v-model:value="curDetailRow.itemName" />
          </n-form-item>

          <!-- 规格型号 -->
          <n-form-item class="form-item-wrapper" label="规格型号">
            <n-input class="item-input" v-model:value="curDetailRow.itemModel" />
          </n-form-item>

          <!-- 单位 -->
          <n-form-item class="form-item-wrapper" label="单位">
            <n-input class="item-input" v-model:value="curDetailRow.itemUnit" />
          </n-form-item>

          <!-- 已送货数量 -->
          <n-form-item class="form-item-wrapper" label="可送货数量">
            <n-input-number class="item-input" v-model:value="curDetailRow.deliverablequan" />
          </n-form-item>

          <!-- 待送货数量 -->
          <n-form-item class="form-item-wrapper" label="送货数量">
            <n-input-number class="item-input" v-model:value="curDetailRow.quan" />
          </n-form-item>


          <!-- 急料标识 -->
          <n-form-item class="form-item-wrapper" label="急料标识">
            <n-select v-model:value="curDetailRow.urgentflag" :options="[
              { label: '非急料', value: '非急料' }, { label: '急料', value: '急料' }
            ]" />
          </n-form-item>
        </n-form>

        <!-- 明细表格批量操作工具栏 -->
        <div class="detail-batch-toolbar">
          <n-space justify="space-between">
            <n-text type="info">
              {{ detailCheckedRowKeys.length > 0 ? `已选择 ${detailCheckedRowKeys.length} 条明细记录` : '未选择任何明细记录' }}
            </n-text>
            <n-space>
              <n-button :size="$global.uiSize.value" type="error" @click="handleDetailBatchDelete"
                :disabled="detailCheckedRowKeys.length === 0">
                <template #icon>
                  <mb-icon icon="TrashOutline" />
                </template>
                批量删除
              </n-button>
              <n-button :size="$global.uiSize.value" type="warning" @click="handleDetailBatchEdit"
                :disabled="detailCheckedRowKeys.length === 0">
                <template #icon>
                  <mb-icon icon="CreateOutline" />
                </template>
                批量编辑
              </n-button>
              <n-button :size="$global.uiSize.value" @click="detailCheckedRowKeys = []; detailSelectedRows = []"
                :disabled="detailCheckedRowKeys.length === 0">
                取消选择
              </n-button>
            </n-space>
          </n-space>
        </div>

        <!-- 明细表格 -->
        <mb-editor-table ref="detailTableRef" v-bind="detailTableOptions"
          v-model:checked-row-keys="detailCheckedRowKeys" @update:checked-row-datas="handleDetailCheckedRowKeysChange"
          @selectedRow="selectDetailRow" :style="{
            flex: 1,
            marginBottom: '16px',
            height: detailTableHeight + 'px',
            maxHeight: detailTableHeight + 'px'
          }" />

        <template #footer>
          <n-space>
            <n-button @click="showDrawer = false">
              取消
            </n-button>
            <n-button type="primary" @click="saveOrderData" :loading="printLoading">
              保存
            </n-button>
            <n-button type="info" @click="handlePrintPreview" :loading="previewLoading">
              <template #icon>
                <mb-icon icon="EyeOutline" />
              </template>
              打印预览
            </n-button>
            <n-button type="info" @click="handlePrint" :loading="printLoading">
              <template #icon>
                <mb-icon icon="PrintOutline" />
              </template>
              打印送货单
            </n-button>
          </n-space>
        </template>
      </n-drawer-content>
    </n-drawer>

    <!-- 打印预览对话框 -->
    <n-modal v-model:show="showPrintPreview" preset="dialog" title="打印预览" style="width: 90%; max-width: 1200px;">
      <template #header>
        <div style="display: flex; align-items: center; gap: 8px;">
          <mb-icon icon="EyeOutline" />
          <span>打印预览</span>
        </div>
      </template>

      <div class="print-preview-container">
        <div v-if="previewLoading" class="preview-loading">
          <n-spin size="large">
            <template #description>
              正在生成预览...
            </template>
          </n-spin>
        </div>
        <div v-else-if="previewHtml" class="preview-content" v-html="previewHtml"></div>
        <div v-else class="preview-empty">
          <n-empty description="暂无预览内容" />
        </div>
      </div>

      <template #action>
        <n-space>
          <n-button @click="showPrintPreview = false">
            关闭
          </n-button>
          <n-button type="primary" @click="handlePrint" :loading="printLoading">
            <template #icon>
              <mb-icon icon="PrintOutline" />
            </template>
            确认打印
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>

import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { useMessage, useDialog, useLoadingBar } from 'naive-ui'

// ==================== 响应式状态管理 ====================
const $message = useMessage()
const $dialog = useDialog()
const $loadingBar = useLoadingBar()

// 控制组件显示状态
const showDrawer = ref(false)
const tableLoading = ref(false)
const printLoading = ref(false)

// 打印预览相关状态
const showPrintPreview = ref(false)
const previewHtml = ref('')
const previewLoading = ref(false)

// 订单相关状态
const orderRowId = ref(null)
const selectedOrderId = computed(() => orderRowId.value)
const detailTableRef = ref()
const table = ref()

// ==================== 多选功能状态管理 ====================
// 主档表格多选状态
const masterCheckedRowKeys = ref([])
const masterSelectedRows = ref([])

// 明细表格多选状态
const detailCheckedRowKeys = ref([])
const detailSelectedRows = ref([])

// 表格高度自适应
const tableHeight = ref(400)
const detailTableHeight = ref(300)

// ==================== 事件处理函数 ====================
// 订单主表选中行发生变化时更新 ID
const orderRowChanged = (row) => {
  orderRowId.value = row?.sguid || null
  console.log('选中订单ID:', orderRowId.value)
}

// ==================== 多选功能事件处理 ====================
// 主档表格多选变化处理
const handleMasterCheckedRowKeysChange = (rows) => {
  masterSelectedRows.value = rows
  console.log('主档表格选中行数:', rows.length)
}

// 明细表格多选变化处理
const handleDetailCheckedRowKeysChange = (rows) => {
  detailSelectedRows.value = rows
  console.log('明细表格选中行数:', rows.length)
}

// ==================== 批量操作功能 ====================
// 主档表格批量操作
const handleMasterBatchDelete = () => {
  if (masterCheckedRowKeys.value.length === 0) {
    $message.warning('请先选择要删除的数据')
    return
  }

  $dialog.warning({
    title: '确认删除',
    content: `确定要删除选中的 ${masterCheckedRowKeys.value.length} 条记录吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 这里添加批量删除的API调用
        $message.success(`成功删除 ${masterCheckedRowKeys.value.length} 条记录`)
        masterCheckedRowKeys.value = []
        masterSelectedRows.value = []
        reloadTable()
      } catch (error) {
        $message.error('批量删除失败')
      }
    }
  })
}

const handleMasterBatchEdit = () => {
  if (masterCheckedRowKeys.value.length === 0) {
    $message.warning('请先选择要编辑的数据')
    return
  }
  $message.info(`批量编辑 ${masterCheckedRowKeys.value.length} 条记录`)
  // 这里可以打开批量编辑对话框
}

const handleMasterBatchExport = () => {
  if (masterCheckedRowKeys.value.length === 0) {
    $message.warning('请先选择要导出的数据')
    return
  }
  $message.info(`批量导出 ${masterCheckedRowKeys.value.length} 条记录`)
  // 这里添加批量导出逻辑
}

// 明细表格批量操作
const handleDetailBatchDelete = () => {
  if (detailCheckedRowKeys.value.length === 0) {
    $message.warning('请先选择要删除的明细数据')
    return
  }

  $dialog.warning({
    title: '确认删除',
    content: `确定要删除选中的 ${detailCheckedRowKeys.value.length} 条明细记录吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 从明细数据中移除选中的行
        detailData.value = detailData.value.filter(item =>
          !detailCheckedRowKeys.value.includes(item.sguid || item.id)
        )
        detailTableRef.value?.setData(detailData.value)
        $message.success(`成功删除 ${detailCheckedRowKeys.value.length} 条明细记录`)
        detailCheckedRowKeys.value = []
        detailSelectedRows.value = []
      } catch (error) {
        $message.error('批量删除失败')
      }
    }
  })
}

const handleDetailBatchEdit = () => {
  if (detailCheckedRowKeys.value.length === 0) {
    $message.warning('请先选择要编辑的明细数据')
    return
  }
  $message.info(`批量编辑 ${detailCheckedRowKeys.value.length} 条明细记录`)
  // 这里可以添加批量编辑逻辑
}

// ==================== 表格高度自适应 ====================
const calculateTableHeight = () => {
  // 计算主表格可用高度
  const windowHeight = window.innerHeight
  const headerHeight = 120 // 搜索区域 + 筛选标签 + 工具栏高度
  const toolbarHeight = 60 // 批量操作工具栏高度
  const footerHeight = 50 // 底部留白

  tableHeight.value = Math.max(300, windowHeight - headerHeight - toolbarHeight - footerHeight)

  // 计算明细表格可用高度（在抽屉中）
  const drawerHeaderHeight = 80
  const formHeight = 400 // 表单区域高度
  const drawerFooterHeight = 80

  detailTableHeight.value = Math.max(250, 600 - drawerHeaderHeight - formHeight - drawerFooterHeight)
}

// 监听窗口大小变化
const handleResize = () => {
  calculateTableHeight()
}

// 组件挂载时计算高度并监听窗口变化
onMounted(() => {
  calculateTableHeight()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// tab页筛选条件配置
const filterConditionSchema = reactive([
  {
    name: 'mesStatus',
    activeKey: 'all', // 推荐将默认激活的key设置为 'all'
    items: [
      {
        field: 'all',
        label: '全部',
        color: 'grey', // 使用一个柔和的灰色或中性色来表示“全部”，不赋予具体状态含义
        count: 0,
      },
      {
        field: '0',
        label: '未审核',
        color: 'red', // 红色，表示待处理、未完成、需要关注
        count: 0,
      },
      {
        // ?? 重要的修正：假设 '已审核' 的 field 是 '1'。请根据你的实际业务调整！
        field: '1',
        label: '已审核',
        color: 'green', // 绿色，表示审核通过、状态良好
        count: 0,
      },
      {
        field: '2',
        label: '出货中',
        color: 'blue', // 蓝色，表示进行中的状态
        count: 0,
      },
      {
        // ?? 重要的修正：假设 '已出货' 的 field 是 '20'。请根据你的实际业务调整！
        field: '3',
        label: '已出货',
        color: 'green', // 绿色，表示出货流程已最终完成
        count: 0,
      },
    ],
  }])

// ==================== 数据状态管理 ====================
const detailData = ref([])
const bill = reactive({
  master: {},
  detail00: []
})
const curDetailRow = ref({})

// ==================== 数据获取和处理函数 ====================
// 获取订单详情数据，并打开抽屉
const fetchDetail = async () => {
  if (!orderRowId.value) {
    $message.warning("请先选择一行数据")
    return
  }

  try {
    $loadingBar.start()
    const res = await $common.get('/srm/outstock/loadbill', {
      sguid: orderRowId.value
    })

    if (res.data) {
      bill.master = res.data.master || {}
      const details = res.data.detail00 || []

      if (Array.isArray(details) && details.length > 0) {
        curDetailRow.value = { ...details[0] }
      } else {
        curDetailRow.value = {}
      }

      detailData.value = details
      showDrawer.value = true
      $loadingBar.finish()
      $message.success("订单详情加载成功")
    }
  } catch (error) {
    console.error("加载订单详情失败:", error)
    $message.error("加载订单详情失败")
    $loadingBar.error()
  }
}

const onDrawerOpen = () => {
  detailTableRef.value?.setData(detailData.value)
}
// ==================== 打印功能实现 ====================
const handlePrint = async () => {
  const hiprintTemplate = new hiprint.PrintTemplate({
    template: masterPrintTemplate, // 模板json对象
  });
  hiprintTemplate.print({
    number: bill.master.billnum,
    date: bill.master.billdate
  });
}

// 打印预览功能
const handlePrintPreview = async () => {
  const hiprintTemplate = new hiprint.PrintTemplate({
    template: masterPrintTemplate, // 模板json对象
  });
  const jqueryObj = hiprintTemplate.getHtml({
    number: bill.master.billnum,
    date: bill.master.billdate
  })
  const html = jqueryObj.html()
  // 设置预览内容
  previewHtml.value = html
  showPrintPreview.value = true
}

// ==================== 表格配置 ====================
const tableOptions = ref({
  url: '/srm/outstock/outstockquery',
  page: true,
  selection: true, // 启用多选功能
  rowKey: 'sguid', // 设置行键
  virtualScroll: true, // 启用虚拟滚动
  where: {
    billnum: {
      label: '采购单号',
      component: 'input',
      props: {
        clearable: true,
        placeholder: '请输入采购单号'
      }
    },
    pbillDate: {
      label: '日期',
      component: 'date',
      props: {
        type: 'daterange',
        clearable: true,
        width: '400px'
      },
      width: '400px'
    },
    suppName: {
      label: '供应商',
      component: 'input',
      props: {
        clearable: true,
        placeholder: '请输入供应商'
      }
    },
  },
  cols: [
    {
      "field": "billnum",
      "label": "采购单号",
      "width": 120
    },
    {
      "field": "billdate",
      "label": "单据日期",
      "width": 120
    },
    {
      "field": "pbillType",
      "label": "单据类型",
      "width": 120
    },
    {
      "field": "suppName",
      "label": "供应商",
      "width": 220
    },
    {
      "field": "pbillDate",
      "label": "采购日期",
      "width": 120
    },
    {
      "field": "mesStatus",
      "label": "状态",
      "width": 120
    },
    {
      "field": "actived",
      "label": "是否使用",
      "width": 120
    },
    {
      "field": "memo",
      "label": "备注",
      "width": 120
    },
    {
      "field": "maker",
      "label": "创建人",
      "width": 200
    },
    {
      "field": "makedate",
      "label": "创建日期",
      "width": 120
    },
    {
      "field": "modifyby",
      "label": "修改人",
      "width": 180
    },
    {
      "field": "modifydate",
      "label": "修改日期",
      "width": 120
    },
    {
      "field": "factroy",
      "label": "工厂",
      "width": 120
    },
    {
      "field": "company",
      "label": "公司",
      "width": 120
    }
  ],
  // 双击事件，用于弹出抽屉
  onDblclick: ({ row }) => {
    orderRowId.value = row.sguid
    fetchDetail()
  },
  afterLoadData: (data) => {
    // 检查 data.filterCondition 是否存在
    if (data && data.filterCondition) {
      // 遍历 filterConditionSchema 中的每个筛选类别（如 mesStatus, orderDate）
      filterConditionSchema.forEach(filterCategory => {
        const categoryName = filterCategory.name; // 获取类别名称，如 'mesStatus' 或 'orderDate'
        const counts = data.filterCondition[categoryName]; // 尝试从后端数据中获取该类别的计数
        if (counts) { // 如果后端返回了该类别的计数数据
          // 遍历当前 filterCategory 的所有 items
          filterCategory.items.forEach(item => {
            // 如果后端数据中有对应 field 的计数，则更新 count，否则默认为 0
            item.count = counts[item.field] !== undefined ? counts[item.field] : 0;
          });
        } else {
          // 如果后端没有返回此 categoryName 的任何数据，将所有 count 重置为 0
          filterCategory.items.forEach(item => {
            item.count = 0;
          });
        }
      });
    } else {
      // 如果 data.filterCondition 不存在，所有计数都重置为 0
      filterConditionSchema.forEach(filterCategory => {
        filterCategory.items.forEach(item => {
          item.count = 0;
        });
      });
    }
  }
})

// ==================== 筛选和表格操作函数 ====================
const handleFilterTabChange = (filterName, value) => {
  console.log(`筛选条件变更: ${filterName} = ${value}`)

  if (filterName === 'mesStatus') {
    if (!tableOptions.value.where.mesStatus) {
      tableOptions.value.where.mesStatus = {}
    }

    if (value === 'all') {
      delete tableOptions.value.where.mesStatus.value
    } else {
      tableOptions.value.where.mesStatus.value = value
    }
  }
  reloadTable()
}

const reloadTable = () => {
  tableLoading.value = true
  table.value?.reload()
  setTimeout(() => {
    tableLoading.value = false
  }, 500)
}

// ==================== 数据监听和响应式处理 ====================
// 监听明细行数据变化，同步更新到明细表格
watch(() => curDetailRow.value, (newValue) => {
  if (newValue && newValue.sguid) {
    const index = detailData.value.findIndex(item => item.sguid === newValue.sguid)
    if (index !== -1) {
      detailData.value.splice(index, 1, { ...newValue })
      detailTableRef.value?.setData(detailData.value)
    }
  }
}, { deep: true })

const searchOptions = reactive({
  fields: ['BillNum']
})

const editorTableFormOptions = ref({
  height: 400,
  width: 800,
  multiple: false,
  showsearch: false,
  onSelectData({ selectData }) {
    if (selectData) {
      curDetailRow.value.itemNo = selectData.itemNo
      curDetailRow.value.itemName = selectData.itemName
      curDetailRow.value.itemModel = selectData.itemModel
      curDetailRow.value.rowno = selectData.rowno
      curDetailRow.value.billnum = selectData.billnum
    }
  },
  tableOptions: {
    url: '/srm/datasource/poorderDropdownList',
    page: true,
    selection: false,
    cols: [
      { field: 'billnum', label: '采购订单号', width: 100 },
      { field: 'rowno', label: '采购订单行号', width: 80 },
      { field: 'itemNo', label: '物料编号', width: 100 },
      { field: 'itemName', label: '物料名称', width: 100 },
      { field: 'itemModel', label: '规格型号', width: 120 },
      { field: 'itemUnit', label: '单位', width: 60 },
      { field: 'quantity', label: '订单数量', width: 80 },
      { field: 'quan', label: '送货数量', width: 80 },
      { field: 'fqty', label: '待送货数量', width: 80 },
      { field: 'needdate', label: '需求日期', width: 80 },
      { field: 'suppName', label: '供应商名称', width: 150 }
    ]
  }
})

const editorTableOptions = ref({
  height: 400,
  width: 800,
  multiple: false,
  onSelectData({ selectData, editorCurrentRow }) {
    console.log('选中数据:', selectData)
    console.log('当前编辑行:', editorCurrentRow)

    if (selectData && editorCurrentRow) {
      editorCurrentRow.itemNo = selectData.itemNo
      editorCurrentRow.itemName = selectData.itemName
      editorCurrentRow.itemModel = selectData.itemModel
      editorCurrentRow.rowno = selectData.rowno
      editorCurrentRow.billnum = selectData.billnum
    }
  },
  search: { fields: ['BillNum'] },
  showsearch: false,
  tableOptions: {
    url: '/srm/datasource/poorderDropdownList',
    page: true,
    selection: false,
    cols: [
      { field: 'billnum', label: '采购订单号', width: 100 },
      { field: 'rowno', label: '采购订单行号', width: 80 },
      { field: 'itemNo', label: '物料编号', width: 100 },
      { field: 'itemName', label: '物料名称', width: 100 },
      { field: 'itemModel', label: '规格型号', width: 120 },
      { field: 'itemUnit', label: '单位', width: 60 },
      { field: 'quantity', label: '订单数量', width: 80 },
      { field: 'quan', label: '送货数量', width: 80 },
      { field: 'fqty', label: '待送货数量', width: 80 },
      { field: 'needdate', label: '需求日期', width: 80 },
      { field: 'suppName', label: '供应商名称', width: 150 }
    ]
  }
})

const masterPrintTemplate = {
  "panels": [
    {
      "index": 0,
      "name": 1,
      "paperType": "自定义",
      "height": 45,
      "width": 64,
      "paperHeader": 0,
      "paperFooter": 127.55905511811025,
      "printElements": [
        {
          "options": {
            "left": 10.5,
            "top": -123,
            "height": 9.75,
            "width": 120
          },
          "printElementType": {
            "title": "文本",
            "type": "text"
          }
        },
        {
          "options": {
            "left": 49.5,
            "top": 21,
            "height": 9.75,
            "width": 120,
            "title": "销售送货单",
            "coordinateSync": false,
            "widthHeightSync": false,
            "fontSize": 15,
            "fontWeight": "bold",
            "qrCodeLevel": 0
          },
          "printElementType": {
            "title": "文本",
            "type": "text"
          }
        },
        {
          "options": {
            "left": 36,
            "top": 60,
            "height": 9.75,
            "width": 120,
            "title": "送货单号",
            "field": "number",
            "coordinateSync": false,
            "widthHeightSync": false,
            "qrCodeLevel": 0
          },
          "printElementType": {
            "title": "文本",
            "type": "text"
          }
        },
        {
          "options": {
            "left": 36,
            "top": 81,
            "height": 9.75,
            "width": 120,
            "title": "送货日期",
            "field": "date",
            "coordinateSync": false,
            "widthHeightSync": false,
            "qrCodeLevel": 0
          },
          "printElementType": {
            "title": "文本",
            "type": "text"
          }
        }
      ],
      "paperNumberLeft": 151,
      "paperNumberTop": 105,
      "paperNumberContinue": true,
      "overPrintOptions": {
        "content": "",
        "opacity": 0.7,
        "type": 1
      },
      "watermarkOptions": {
        "content": "",
        "fillStyle": "rgba(184, 184, 184, 0.3)",
        "fontSize": "14px",
        "rotate": 25,
        "width": 200,
        "height": 200,
        "timestamp": false,
        "format": "YYYY-MM-DD HH:mm"
      },
      "panelLayoutOptions": {
        "layoutType": "column",
        "layoutRowGap": 0,
        "layoutColumnGap": 0
      }
    }
  ]
}

const detailPrintTemplate = {
  "panels": [
    {
      "index": 0,
      "name": 1,
      "paperType": "自定义",
      "height": 45,
      "width": 64,
      "paperHeader": 0,
      "paperFooter": 127.55905511811025,
      "printElements": [
        {
          "options": {
            "left": 36,
            "top": 16.5,
            "height": 30,
            "width": 100,
            "textType": "barcode",
            "title": "barcode",
            "field": "barcode",
            "coordinateSync": false,
            "widthHeightSync": false,
            "qrCodeLevel": 0
          },
          "printElementType": {
            "title": "barcode",
            "type": "text"
          }
        },
        {
          "options": {
            "left": 36,
            "top": 60,
            "height": 9.75,
            "width": 120,
            "title": "产品编码",
            "field": "number",
            "coordinateSync": false,
            "widthHeightSync": false,
            "qrCodeLevel": 0
          },
          "printElementType": {
            "title": "文本",
            "type": "text"
          }
        },
        {
          "options": {
            "left": 36,
            "top": 81,
            "height": 9.75,
            "width": 120,
            "title": "产品名称",
            "field": "name",
            "coordinateSync": false,
            "widthHeightSync": false,
            "qrCodeLevel": 0
          },
          "printElementType": {
            "title": "文本",
            "type": "text"
          }
        }
      ],
      "paperNumberLeft": 151,
      "paperNumberTop": 105,
      "paperNumberContinue": true,
      "overPrintOptions": {
        "content": "",
        "opacity": 0.7,
        "type": 1
      },
      "watermarkOptions": {
        "content": "",
        "fillStyle": "rgba(184, 184, 184, 0.3)",
        "fontSize": "14px",
        "rotate": 25,
        "width": 200,
        "height": 200,
        "timestamp": false,
        "format": "YYYY-MM-DD HH:mm"
      },
      "panelLayoutOptions": {
        "layoutType": "column",
        "layoutRowGap": 0,
        "layoutColumnGap": 0
      }
    }
  ]
}

const detailTableOptions = ref({
  page: false,
  selection: true, // 启用多选功能
  rowKey: 'sguid', // 设置行键，如果没有sguid则使用id
  virtualScroll: true, // 启用虚拟滚动
  cols: [
    {
      field: 'billnum', label: '采购单号', width: 120, component: 'select-table',
      componentProps: editorTableOptions.value,
      componentStyle: "height:100%", alwaysEdit: false
    },
    { field: 'purorderseq', label: '采购订单行号', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'itemno', label: '物料编号', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'itemName', label: '物料名称', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'itemModel', label: '规格型号', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'itemUnit', label: '单位', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'deliverablequan', label: '可送货数量', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    { field: 'quan', label: '送货数量', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    {
      field: 'urgentflag', label: '急料标识', width: 120, component: 'select',
      componentProps: {
        options: [
          { label: '非急料', value: '非急料' }, { label: '急料', value: '急料' }
        ]
      },
      componentStyle: "height:100%", alwaysEdit: false
    },
    { field: 'RemarkSub', label: '备注', width: 120, component: 'input', componentStyle: "height:100%", alwaysEdit: false },
    {
      label: '操作',
      type: 'buttons',
      width: 220,
      fixed: 'right',
      buttons: [
        {
          label: '预览',
          link: true,
          click: async (row) => {
            try {
              previewLoading.value = true
              const res = await $common.post('/srm/bar/barprint', row);
              const printDatas = res.data.map(item => {
                return {
                  number: item.expFieldname1,
                  name: item.expFieldname3,
                  barcode: item.expFieldname2
                }
              })

              const hiprintTemplate = new hiprint.PrintTemplate({
                template: detailPrintTemplate, // 模板json对象
              });

              // 获取预览 html
              const jqueryObj = hiprintTemplate.getHtml(printDatas);
              const html = jqueryObj.html();

              // 设置预览内容并显示对话框
              previewHtml.value = html
              showPrintPreview.value = true

              console.log('预览HTML生成成功', html);
            } catch (error) {
              console.error('生成预览失败:', error)
              $message.error('生成预览失败')
            } finally {
              previewLoading.value = false
            }
          }
        },
        {
          label: '打印',
          link: true,
          click: async (row) => {
            const res = await $common.post('/srm/bar/barprint', row);
            const printDatas = res.data.map(item => {
              return {
                number: item.expFieldname1,
                name: item.expFieldname3,
                barcode: item.expFieldname2
              }
            })
            console.log('print', printDatas);
            const hiprintTemplate = new hiprint.PrintTemplate({
              template: detailPrintTemplate, // 模板json对象
            });
            hiprintTemplate.print(printDatas);
          }
        },
        {
          label: '删除',
          link: true,
          textColor: 'red',
          click: (row) => {

          }
        }
      ]
    }
  ]
})

const selectDetailRow = (row) => {
  console.log('选中了明细行', row)
  curDetailRow.value = row
}

// 保存订单数据
const saveOrderData = async () => {
  if (!bill.master || !Object.keys(bill.master).length) {
    $message.warning("请先选择订单")
    return
  }

  try {
    $loadingBar.start()
    const saveData = {
      master: bill.master,
      detail: detailData.value
    }

    const res = await $common.post('/srm/outstock/save', saveData)
    if (res.success) {
      $message.success("保存成功")
      showDrawer.value = false
      reloadTable()
    } else {
      $message.error(res.message || "保存失败")
    }
    $loadingBar.finish()
  } catch (error) {
    console.error("保存失败:", error)
    $message.error("保存失败")
    $loadingBar.error()
  }
}
</script>

<style scoped>
.main-container {
  width: 100%;
  height: 100%;
  display: inline-flex;
  flex-direction: column;
}

.header-row {
  height: fit-content;
  padding: 16px 0;
}

.search-row {
  padding-bottom: 12px;
}

/* 批量操作工具栏样式 */
.batch-toolbar {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 5px 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.batch-toolbar :deep(.n-space) {
  align-items: center;
  width: 100%;
}

.batch-toolbar :deep(.n-text) {
  line-height: 1.5;
  display: flex;
  align-items: center;
}

.detail-batch-toolbar {
  background-color: #f0f8ff;
  border: 1px solid #d1ecf1;
  border-radius: 6px;
  padding: 5px 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detail-batch-toolbar :deep(.n-space) {
  align-items: center;
  width: 100%;
}

.detail-batch-toolbar :deep(.n-text) {
  line-height: 1.5;
  display: flex;
  align-items: center;
}

/* 打印预览对话框样式 */
.print-preview-container {
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #e0e0e6;
  border-radius: 6px;
  background-color: #fafafa;
}

.preview-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.preview-content {
  padding: 20px;
  background-color: white;
  margin: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

/* 确保预览内容中的样式正确显示 */
.preview-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
}

.preview-content :deep(td),
.preview-content :deep(th) {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.preview-content :deep(th) {
  background-color: #f5f5f5;
  font-weight: bold;
}

/* 抽屉内部的表单样式，与原先 Tab 内部的样式保持一致 */

.order-form .form-item-wrapper {
  width: 320px;
  display: inline-flex;
  margin: 4px 8px;
}

.order-form .form-item-wrapper .n-form-item-blank {
  flex: 1;
  color: red;
}

.order-form .form-item-wrapper .item-input {
  flex: 1;
}

:deep(.magic-select-table > .mb-list) {
  padding: 1px;
}
</style>
<style>
.n-tab-pane {
  display: inline-flex;
  flex-direction: column;
}

.n-tabs-pane-wrapper {
  flex: 1;
  width: 100%;
}

.n-form-item-blank {
  flex: 1;
}
</style>