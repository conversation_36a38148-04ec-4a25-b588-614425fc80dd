<script setup>
import { computed } from 'vue';
import MbIcon from '@/components/magic/basic/mb-icon.vue';

defineOptions({
  name: 'FirstLevelMenu'
});

const props = defineProps({
  menus: {
    type: Array,
    default: () => []
  },
  activeMenuKey: {
    type: String,
    default: ''
  },
  inverted: {
    type: Boolean,
    default: false
  },
  siderCollapse: {
    type: Boolean,
    default: false
  },
  darkMode: {
    type: Boolean,
    default: false
  },
  themeColor: {
    type: String,
    default: '#646cff'
  }
});

const emit = defineEmits(['select', 'toggle-sider-collapse']);

// 处理第一级菜单数据（移除子菜单）
const firstLevelMenuOptions = computed(() => {
  return props.menus.map(menu => ({
    label: menu.label,
    key: menu.key,
    icon: menu.icon
  }));
});

// 计算激活状态的背景色
const selectedBgColor = computed(() => {
  const { themeColor, darkMode } = props;

  if (darkMode) {
    // 深色模式下使用更深的颜色
    return `color-mix(in srgb, ${themeColor} 30%, #000000)`;
  } else {
    // 浅色模式下使用更浅的颜色
    return `color-mix(in srgb, ${themeColor} 10%, #ffffff)`;
  }
});

function handleMenuSelect(key) {
  const menu = props.menus.find(m => m.key === key);
  if (menu) {
    emit('select', menu);
  }
}

function handleToggleSiderCollapse() {
  emit('toggle-sider-collapse');
}
</script>

<template>
  <div class="first-level-menu h-full flex flex-col">
    <!-- Logo 区域 -->
    <div class="logo-area flex items-center justify-center" style="height: 56px;">
      <slot>
        <div v-if="!siderCollapse" class="text-lg font-bold text-primary">
          Logo
        </div>
        <div v-else class="text-lg font-bold text-primary">
          L
        </div>
      </slot>
    </div>

    <!-- 菜单区域 -->
    <div class="flex-1 overflow-hidden">
      <n-scrollbar class="h-full">
        <div class="mix-menu-list">
          <div v-for="menu in menus" :key="menu.key" class="mix-menu-item" :class="{
            'mix-menu-item--active': menu.key === activeMenuKey,
            'mix-menu-item--collapsed': siderCollapse,
            'mix-menu-item--inverted': inverted
          }" @click="handleMenuSelect(menu.key)">
            <div class="mix-menu-item__icon">
              <MbIcon v-if="menu.icon" :icon="menu.icon" size="18px" />
              <n-icon v-else size="18">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor"
                    d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                </svg>
              </n-icon>
            </div>
            <div v-if="!siderCollapse" class="mix-menu-item__label">
              {{ menu.label }}
            </div>
          </div>
        </div>
      </n-scrollbar>
    </div>

    <!-- 折叠按钮 -->
    <div class="collapse-btn-area p-2">
      <n-button quaternary size="small" @click="handleToggleSiderCollapse">
        <template #icon>
          <n-icon size="16">
            <svg viewBox="0 0 24 24">
              <path fill="currentColor"
                :d="siderCollapse
                  ? 'M9.29 15.88L13.17 12 9.29 8.12a.996.996 0 1 1 1.41-1.41l4.59 4.59c.39.39.39 1.02 0 1.41L10.7 17.3a.996.996 0 0 1-1.41-1.42z'
                  : 'M14.71 15.88L10.83 12l3.88-3.88a.996.996 0 1 0-1.41-1.41L8.71 11.3a.996.996 0 0 0 0 1.41l4.59 4.59c.39.39 1.02.39 1.41 0 .38-.39.39-1.03 0-1.42z'" />
            </svg>
          </n-icon>
        </template>
      </n-button>
    </div>
  </div>
</template>

<style scoped>
.first-level-menu {
  background: var(--n-color);
  border-right: 1px solid var(--n-border-color);
}

.logo-area {
  border-bottom: 1px solid var(--n-border-color);
}

.collapse-btn-area {
  border-top: 1px solid var(--n-border-color);
}

.mix-menu-list {
  padding: 8px 0;
}

.mix-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 2px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.mix-menu-item:hover {
  background-color: var(--n-item-color-hover);
}

.mix-menu-item--active {
  background-color: v-bind(selectedBgColor);
  color: var(--n-item-text-color-active);
}

.mix-menu-item--collapsed {
  justify-content: center;
  padding: 12px 8px;
}

.mix-menu-item--inverted {
  color: rgba(255, 255, 255, 0.82);
}

.mix-menu-item--inverted:hover {
  background-color: rgba(255, 255, 255, 0.09);
}

.mix-menu-item--inverted.mix-menu-item--active {
  background-color: rgba(255, 255, 255, 0.15);
  color: #ffffff;
}

.mix-menu-item__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.mix-menu-item__label {
  margin-left: 12px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mix-menu-item--collapsed .mix-menu-item__label {
  display: none;
}
</style>
