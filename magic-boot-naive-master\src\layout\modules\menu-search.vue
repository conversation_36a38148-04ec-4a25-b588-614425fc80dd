<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'MenuSearch'
});

const props = defineProps({
  menuOptions: {
    type: Array,
    default: () => []
  }
});

const router = useRouter();
const searchValue = ref('');
const showSearch = ref(false);

const filteredMenus = computed(() => {
  if (!searchValue.value) return [];
  
  const result = [];
  
  function searchInMenus(menus, parentPath = '') {
    menus.forEach(menu => {
      if (menu.title && menu.title.toLowerCase().includes(searchValue.value.toLowerCase())) {
        result.push({
          ...menu,
          fullPath: parentPath + menu.path
        });
      }
      
      if (menu.children) {
        searchInMenus(menu.children, parentPath + menu.path);
      }
    });
  }
  
  searchInMenus(props.menuOptions);
  return result.slice(0, 10); // 限制显示数量
});

function handleSearch() {
  showSearch.value = true;
}

function handleSelect(menu) {
  if (menu.openMode === '1') {
    window.open(menu.path);
  } else {
    router.push(menu.path);
  }
  showSearch.value = false;
  searchValue.value = '';
}

function handleClose() {
  showSearch.value = false;
  searchValue.value = '';
}
</script>

<template>
  <div class="menu-search">
    <!-- Search Trigger -->
    <div class="search-trigger cursor-pointer" @click="handleSearch">
      <n-icon size="18">
        <svg viewBox="0 0 24 24">
          <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
      </n-icon>
    </div>

    <!-- Search Modal -->
    <n-modal v-model:show="showSearch" preset="card" title="搜索菜单" style="width: 600px;">
      <div class="search-content">
        <n-input
          v-model:value="searchValue"
          placeholder="搜索菜单..."
          clearable
          autofocus
          @keyup.enter="handleSelect(filteredMenus[0])"
          @keyup.esc="handleClose"
        >
          <template #prefix>
            <n-icon>
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </n-icon>
          </template>
        </n-input>

        <div v-if="searchValue && filteredMenus.length > 0" class="search-results mt-4">
          <div
            v-for="(menu, index) in filteredMenus"
            :key="menu.id || index"
            class="search-item p-3 cursor-pointer hover:bg-gray-50 rounded"
            @click="handleSelect(menu)"
          >
            <div class="flex items-center">
              <n-icon v-if="menu.icon" class="mr-2">
                <component :is="menu.icon" />
              </n-icon>
              <div>
                <div class="font-medium">{{ menu.title }}</div>
                <div v-if="menu.fullPath" class="text-sm text-gray-500">{{ menu.fullPath }}</div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="searchValue && filteredMenus.length === 0" class="no-results mt-4 text-center text-gray-500">
          未找到相关菜单
        </div>

        <div v-else class="search-tips mt-4 text-center text-gray-400">
          输入关键词搜索菜单
        </div>
      </div>
    </n-modal>
  </div>
</template>

<style scoped>
.search-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.search-trigger:hover {
  background-color: var(--hover-color, rgba(0, 0, 0, 0.05));
}

.search-item {
  transition: background-color 0.2s;
}

.search-item:hover {
  background-color: var(--hover-color, rgba(0, 0, 0, 0.05));
}
</style>
