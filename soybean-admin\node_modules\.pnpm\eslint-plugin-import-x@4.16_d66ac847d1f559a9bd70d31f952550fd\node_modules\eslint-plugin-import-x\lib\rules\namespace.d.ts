import type { TSESLint } from '@typescript-eslint/utils';
export type MessageId = 'noNamesFound' | 'computedReference' | 'namespaceMember' | 'topLevelNames' | 'notFoundInNamespace' | 'notFoundInNamespaceDeep';
export interface Options {
    allowComputed?: boolean;
}
declare const _default: TSESLint.RuleModule<MessageId, [Options], import("../utils/create-rule.ts").ImportXPluginDocs, TSESLint.RuleListener>;
export default _default;
