/**
 * Store plugin for resetting setup stores
 * @param {Object} context - Pinia plugin context
 */
export function resetSetupStore(context) {
  const setupSyntaxIds = ['app', 'user', 'theme', 'tabs', 'dict'];
  
  if (setupSyntaxIds.includes(context.store.$id)) {
    const { $state } = context.store;
    
    // Create a deep clone of the initial state
    const defaultStore = JSON.parse(JSON.stringify($state));
    
    // Override the $reset method for setup stores
    context.store.$reset = () => {
      context.store.$patch(defaultStore);
    };
  }
}

/**
 * Store plugin for persisting state to localStorage
 * @param {Object} context - Pinia plugin context
 */
export function persistStore(context) {
  const persistIds = ['theme', 'app'];

  if (persistIds.includes(context.store.$id)) {
    // Dynamic import to avoid circular dependency
    import('@/utils/storage').then(({ localStg }) => {
      // Load persisted state on store creation
      const persistedState = localStg.get(`store_${context.store.$id}`);
      if (persistedState) {
        context.store.$patch(persistedState);
      }

      // Save state changes to localStorage
      context.store.$subscribe((mutation, state) => {
        localStg.set(`store_${context.store.$id}`, state);
      });
    });
  }
}

/**
 * Store plugin for logging state changes in development
 * @param {Object} context - Pinia plugin context
 */
export function loggerStore(context) {
  if (import.meta.env.DEV) {
    context.store.$subscribe((mutation, state) => {
      console.group(`🍍 ${mutation.storeId}`);
      console.log('Type:', mutation.type);
      console.log('Payload:', mutation.payload);
      console.log('State:', state);
      console.groupEnd();
    });
  }
}
