import type { TSESLint } from '@typescript-eslint/utils';
export interface Options {
    src?: string[];
    ignoreExports?: string[];
    missingExports?: true;
    unusedExports?: boolean;
    ignoreUnusedTypeExports?: boolean;
}
type MessageId = 'notFound' | 'unused';
declare const _default: TSESLint.RuleModule<MessageId, Options[], import("../utils/create-rule.ts").ImportXPluginDocs, TSESLint.RuleListener>;
export default _default;
