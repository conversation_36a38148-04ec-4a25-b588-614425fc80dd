<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fc0dd5b9-f3f4-4a23-9e84-c534284b302e" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/postcss.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/postcss.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/src/App.vue" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/src/App.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/src/layout/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/src/layout/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/src/store/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/src/store/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/src/store/modules/userStore.js" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/src/store/modules/userStore.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/src/styles/index.less" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/src/styles/index.less" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/src/styles/tailwind.css" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/src/styles/tailwind.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/tailwind.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/tailwind.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/magic-boot-naive-master/vite.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/magic-boot-naive-master/vite.config.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/magic-boot-naive-master" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="31GstPLGG1qHIuzBSqUX8MHwiEZ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/workspace/FrontendProjects/vue/ui-refactor",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "pnpm",
    "npm.dev.executor": "Run",
    "ts.external.directory.path": "D:\\workspace\\FrontendProjects\\vue\\ui-refactor\\soybean-admin\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/magic-boot-naive-master/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.411" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="fc0dd5b9-f3f4-4a23-9e84-c534284b302e" name="更改" comment="" />
      <created>1755161957385</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755161957385</updated>
      <workItem from="1755161959287" duration="838000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>