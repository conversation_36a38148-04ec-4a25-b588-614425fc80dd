<script setup>
import { computed } from 'vue';
import { useTabsStore } from '@/store/modules/tabsStore';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'GlobalTab'
});

const tabsStore = useTabsStore();
const router = useRouter();

const tabs = computed(() => tabsStore.getTabs || []);
const currentTab = computed(() => tabsStore.getCurrentTab);

function handleTabClick(tab) {
  router.push(tab.path);
}

function handleTabClose(tab) {
  tabsStore.removeTab(tab.path);
}

function handleTabContextMenu(e, tab) {
  e.preventDefault();
  // 这里可以添加右键菜单逻辑
  console.log('Tab context menu:', tab);
}
</script>

<template>
  <div class="global-tab h-full flex items-center bg-container shadow-tab">
    <div class="tab-container flex-1 h-full flex items-center overflow-hidden">
      <n-scrollbar x-scrollable class="flex-1">
        <div class="tab-list flex h-full">
          <div
            v-for="tab in tabs"
            :key="tab.path"
            class="tab-item flex items-center px-3 cursor-pointer border-r border-gray-200"
            :class="{
              'tab-active': tab.path === currentTab,
              'tab-inactive': tab.path !== currentTab
            }"
            @click="handleTabClick(tab)"
            @contextmenu="handleTabContextMenu($event, tab)"
          >
            <span class="tab-title text-sm">{{ tab.meta?.title || tab.name }}</span>
            <n-icon
              v-if="tabs.length > 1"
              class="tab-close ml-2 opacity-60 hover:opacity-100"
              size="14"
              @click.stop="handleTabClose(tab)"
            >
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </n-icon>
          </div>
        </div>
      </n-scrollbar>
    </div>

    <!-- Tab Actions -->
    <div class="tab-actions flex items-center px-2">
      <n-dropdown
        :options="[
          { label: '关闭其他', key: 'close-others' },
          { label: '关闭左侧', key: 'close-left' },
          { label: '关闭右侧', key: 'close-right' },
          { label: '关闭全部', key: 'close-all' }
        ]"
        @select="handleTabAction"
      >
        <n-icon class="cursor-pointer" size="16">
          <svg viewBox="0 0 24 24">
            <path fill="currentColor" d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
          </svg>
        </n-icon>
      </n-dropdown>
    </div>
  </div>
</template>

<script>
function handleTabAction(key) {
  const tabsStore = useTabsStore();
  
  switch (key) {
    case 'close-others':
      tabsStore.closeOtherTabs();
      break;
    case 'close-left':
      tabsStore.closeLeftTabs();
      break;
    case 'close-right':
      tabsStore.closeRightTabs();
      break;
    case 'close-all':
      tabsStore.closeAllTabs();
      break;
  }
}
</script>

<style scoped>
.shadow-tab {
  box-shadow: var(--tab-box-shadow, 0 1px 2px rgb(0, 21, 41, 0.08));
}

.bg-container {
  background-color: var(--container-color, rgb(255, 255, 255));
}

.tab-item {
  height: 100%;
  min-width: 120px;
  max-width: 200px;
  transition: all 0.2s;
  white-space: nowrap;
}

.tab-active {
  background-color: var(--primary-color-suppl, rgba(100, 108, 255, 0.1));
  color: var(--primary-color, #646cff);
  border-bottom: 2px solid var(--primary-color, #646cff);
}

.tab-inactive {
  background-color: var(--hover-color, rgba(0, 0, 0, 0.02));
}

.tab-inactive:hover {
  background-color: var(--hover-color, rgba(0, 0, 0, 0.05));
}

.tab-title {
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-close {
  transition: opacity 0.2s;
}

.h-full {
  height: 100%;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.overflow-hidden {
  overflow: hidden;
}

.cursor-pointer {
  cursor: pointer;
}

.text-sm {
  font-size: 0.875rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.border-r {
  border-right-width: 1px;
}

.border-gray-200 {
  border-color: rgb(229, 231, 235);
}

.opacity-60 {
  opacity: 0.6;
}

.hover\:opacity-100:hover {
  opacity: 1;
}
</style>
