const { themeVars } = require('./src/theme/vars');

module.exports = {
  content: [
    './index.html',
    './src/**/*.{vue,js,ts,jsx,tsx}'
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: 'rgb(var(--primary-color) / <alpha-value>)',
        info: 'rgb(var(--info-color) / <alpha-value>)',
        success: 'rgb(var(--success-color) / <alpha-value>)',
        warning: 'rgb(var(--warning-color) / <alpha-value>)',
        error: 'rgb(var(--error-color) / <alpha-value>)',
        container: 'rgb(var(--container-bg-color) / <alpha-value>)',
        layout: 'rgb(var(--layout-bg-color) / <alpha-value>)',
        inverted: 'rgb(var(--inverted-bg-color) / <alpha-value>)',
        'base-text': 'rgb(var(--base-text-color) / <alpha-value>)',
        nprogress: 'rgb(var(--nprogress-color) / <alpha-value>)'
      },
      fontSize: {
        'icon-xs': '0.875rem',
        'icon-small': '1rem',
        'icon': '1.125rem',
        'icon-large': '1.5rem',
        'icon-xl': '2rem'
      },
      boxShadow: {
        header: 'var(--header-box-shadow)',
        sider: 'var(--sider-box-shadow)',
        tab: 'var(--tab-box-shadow)'
      },
      spacing: {
        'header': 'var(--header-height)',
        'sider': 'var(--sider-width)',
        'sider-collapsed': 'var(--sider-collapsed-width)'
      }
    }
  },
  plugins: [
    function({ addUtilities, addComponents }) {
      // Add soybean-admin style utilities
      const newUtilities = {
        '.transition-base': {
          'transition': 'all 0.3s ease-in-out'
        },
        '.transition-width-300': {
          'transition': 'width 0.3s ease'
        },
        '.card-wrapper': {
          'border-radius': '0.5rem',
          'box-shadow': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)'
        },
        '.flex-center': {
          'display': 'flex',
          'align-items': 'center',
          'justify-content': 'center'
        },
        '.flex-x-center': {
          'display': 'flex',
          'justify-content': 'center'
        },
        '.flex-y-center': {
          'display': 'flex',
          'align-items': 'center'
        },
        '.flex-col-center': {
          'display': 'flex',
          'flex-direction': 'column',
          'align-items': 'center',
          'justify-content': 'center'
        },
        '.flex-col-stretch': {
          'display': 'flex',
          'flex-direction': 'column',
          'align-items': 'stretch'
        },
        '.i-flex-center': {
          'display': 'inline-flex',
          'align-items': 'center',
          'justify-content': 'center'
        },
        '.i-flex-x-center': {
          'display': 'inline-flex',
          'justify-content': 'center'
        },
        '.i-flex-y-center': {
          'display': 'inline-flex',
          'align-items': 'center'
        },
        '.i-flex-col': {
          'display': 'inline-flex',
          'flex-direction': 'column'
        },
        '.i-flex-col-center': {
          'display': 'inline-flex',
          'flex-direction': 'column',
          'align-items': 'center',
          'justify-content': 'center'
        },
        '.i-flex-col-stretch': {
          'display': 'inline-flex',
          'flex-direction': 'column',
          'align-items': 'stretch'
        },
        '.flex-1-hidden': {
          'flex': '1 1 0%',
          'overflow': 'hidden'
        }
      };
      addUtilities(newUtilities);
    }
  ]
};
