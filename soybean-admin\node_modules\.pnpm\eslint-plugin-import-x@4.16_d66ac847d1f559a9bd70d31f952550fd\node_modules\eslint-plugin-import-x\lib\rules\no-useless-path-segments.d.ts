import type { ModuleOptions } from '../utils/index.js';
export interface Options extends ModuleOptions {
    noUselessIndex?: boolean;
}
export type MessageId = 'useless';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"useless", [(Options | undefined)?], import("../utils/create-rule.ts").ImportXPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
