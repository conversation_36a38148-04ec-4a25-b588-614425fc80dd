<template>
  <n-drawer v-model:show="visible" :width="320" placement="right">
    <n-drawer-content title="主题配置" closable>
      <div class="theme-config">
        <!-- 主题模式 -->
        <div class="config-section">
          <h4>主题模式</h4>
          <n-radio-group v-model:value="themeStore.settings.themeScheme" @update:value="handleThemeSchemeChange">
            <n-space>
              <n-radio value="light">浅色</n-radio>
              <n-radio value="dark">深色</n-radio>
              <n-radio value="auto">跟随系统</n-radio>
            </n-space>
          </n-radio-group>
        </div>

        <!-- 主题色 -->
        <div class="config-section">
          <h4>主题色</h4>
          <div class="color-picker-wrapper">
            <n-color-picker 
              v-model:value="themeStore.settings.themeColor" 
              @update:value="handleThemeColorChange"
              :modes="['hex']"
              :show-alpha="false"
            />
            <span class="color-value">{{ themeStore.settings.themeColor }}</span>
          </div>
        </div>

        <!-- 其他颜色 -->
        <div class="config-section">
          <h4>其他颜色</h4>
          <div class="other-colors">
            <div v-for="(color, key) in themeStore.settings.otherColor" :key="key" class="color-item">
              <span class="color-label">{{ getColorLabel(key) }}</span>
              <n-color-picker 
                v-model:value="themeStore.settings.otherColor[key]" 
                @update:value="(value) => handleOtherColorChange(key, value)"
                :modes="['hex']"
                :show-alpha="false"
                size="small"
              />
            </div>
          </div>
        </div>

        <!-- 布局模式 -->
        <div class="config-section">
          <h4>布局模式</h4>
          <n-radio-group v-model:value="themeStore.settings.layout.mode" @update:value="handleLayoutModeChange">
            <n-space vertical>
              <n-radio value="vertical">垂直布局</n-radio>
              <n-radio value="horizontal">水平布局</n-radio>
              <n-radio value="horizontal-mix">水平混合</n-radio>
              <n-radio value="vertical-mix">垂直混合</n-radio>
            </n-space>
          </n-radio-group>
        </div>

        <!-- 辅助功能 -->
        <div class="config-section">
          <h4>辅助功能</h4>
          <div class="switch-item">
            <span>灰度模式</span>
            <n-switch v-model:value="themeStore.settings.grayscale" @update:value="handleGrayscaleChange" />
          </div>
          <div class="switch-item">
            <span>色弱模式</span>
            <n-switch v-model:value="themeStore.settings.colourWeakness" @update:value="handleColourWeaknessChange" />
          </div>
          <div class="switch-item">
            <span>推荐颜色</span>
            <n-switch v-model:value="themeStore.settings.recommendColor" />
          </div>
        </div>

        <!-- 尺寸配置 -->
        <div class="config-section">
          <h4>尺寸配置</h4>
          <div class="size-item">
            <span>头部高度</span>
            <n-input-number 
              v-model:value="themeStore.settings.header.height" 
              :min="48" 
              :max="80" 
              size="small"
              @update:value="handleHeaderHeightChange"
            />
          </div>
          <div class="size-item">
            <span>标签页高度</span>
            <n-input-number 
              v-model:value="themeStore.settings.tab.height" 
              :min="32" 
              :max="56" 
              size="small"
              @update:value="handleTabHeightChange"
            />
          </div>
          <div class="size-item">
            <span>侧边栏宽度</span>
            <n-input-number 
              v-model:value="themeStore.settings.sider.width" 
              :min="200" 
              :max="300" 
              size="small"
              @update:value="handleSiderWidthChange"
            />
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="config-actions">
          <n-space>
            <n-button @click="handleReset">重置</n-button>
            <n-button type="primary" @click="handleCopy">复制配置</n-button>
          </n-space>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup>
import { ref } from 'vue'
import { useThemeStore } from '@/store/modules/themeStore'
import { useMessage } from 'naive-ui'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])

const visible = ref(props.visible)
const themeStore = useThemeStore()
const message = useMessage()

// 颜色标签映射
const colorLabels = {
  info: '信息色',
  success: '成功色',
  warning: '警告色',
  error: '错误色'
}

function getColorLabel(key) {
  return colorLabels[key] || key
}

function handleThemeSchemeChange(value) {
  themeStore.setThemeScheme(value)
}

function handleThemeColorChange(value) {
  themeStore.setThemeColor(value)
}

function handleOtherColorChange(key, value) {
  themeStore.setOtherColor(key, value)
}

function handleLayoutModeChange(value) {
  themeStore.setThemeLayout(value)
}

function handleGrayscaleChange(value) {
  themeStore.setGrayscale(value)
}

function handleColourWeaknessChange(value) {
  themeStore.setColourWeakness(value)
}

function handleHeaderHeightChange(value) {
  themeStore.setHeaderHeight(value)
}

function handleTabHeightChange(value) {
  themeStore.setTabHeight(value)
}

function handleSiderWidthChange(value) {
  themeStore.setSiderWidth(value)
}

function handleReset() {
  themeStore.resetThemeSettings()
  message.success('主题配置已重置')
}

function handleCopy() {
  navigator.clipboard.writeText(themeStore.settingsJson).then(() => {
    message.success('配置已复制到剪贴板')
  }).catch(() => {
    message.error('复制失败')
  })
}
</script>

<style scoped>
.theme-config {
  padding: 16px 0;
}

.config-section {
  margin-bottom: 24px;
}

.config-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--base-text-color);
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-value {
  font-size: 12px;
  color: var(--base-text-color);
  opacity: 0.7;
}

.other-colors {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.color-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.color-label {
  font-size: 13px;
  color: var(--base-text-color);
}

.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.switch-item span {
  font-size: 13px;
  color: var(--base-text-color);
}

.size-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.size-item span {
  font-size: 13px;
  color: var(--base-text-color);
}

.config-actions {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}
</style>
