import type { TypeOrValueSpecifier } from '../util';
type MessageIds = 'deprecated' | 'deprecatedWithReason';
type Options = [
    {
        allow?: TypeOrValueSpecifier[];
    }
];
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-deprecated.d.ts.map